1:"$Sreact.fragment"
2:I[94970,[],"ClientSegmentRoot"]
3:I[70909,["1778","static/chunks/1778-ff7f41a4327b2f02.js","1915","static/chunks/1915-f4ae9c03b20697e2.js","6961","static/chunks/6961-e978919a41d767a5.js","8592","static/chunks/8592-f00099624b2cafcb.js","6302","static/chunks/6302-479287e0c72c4a46.js","1496","static/chunks/1496-63849f4f47cbfc4c.js","7177","static/chunks/app/layout-45c5bd505fc34d81.js"],"default"]
4:I[87555,[],""]
5:I[31295,[],""]
7:I[90894,[],"ClientPageRoot"]
8:I[14041,["1778","static/chunks/1778-ff7f41a4327b2f02.js","1915","static/chunks/1915-f4ae9c03b20697e2.js","6961","static/chunks/6961-e978919a41d767a5.js","8592","static/chunks/8592-f00099624b2cafcb.js","3118","static/chunks/3118-3eb4b661a189fd39.js","6003","static/chunks/6003-21b2c736ea57d643.js","9693","static/chunks/9693-ec5bfd50fce6e06e.js","4360","static/chunks/app/inventario/page-f691d5bb4e9379f2.js"],"default"]
b:I[59665,[],"OutletBoundary"]
d:I[74911,[],"AsyncMetadataOutlet"]
f:I[59665,[],"ViewportBoundary"]
11:I[59665,[],"MetadataBoundary"]
12:"$Sreact.suspense"
14:I[28393,[],""]
:HL["/_next/static/css/fc37c8c810b334bd.css","style"]
0:{"P":null,"b":"8WeMD5X14Yx8gyQfnpumx","p":"","c":["","inventario"],"i":false,"f":[[["",{"children":["inventario",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/fc37c8c810b334bd.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","$L2",null,{"Component":"$3","slots":{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]},"params":{},"promise":"$@6"}]]}],{"children":["inventario",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"Component":"$8","searchParams":{},"params":"$0:f:0:1:1:props:children:1:props:params","promises":["$@9","$@a"]}],null,["$","$Lb",null,{"children":["$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},[null,[],[]],false]},null,false],["$","$1","h",{"children":[null,[["$","$Lf",null,{"children":"$L10"}],null],["$","$L11",null,{"children":["$","div",null,{"hidden":true,"children":["$","$12",null,{"fallback":null,"children":"$L13"}]}]}]]}],false]],"m":"$undefined","G":["$14",[]],"s":false,"S":true}
6:"$0:f:0:1:1:props:children:1:props:params"
9:{}
a:"$0:f:0:1:1:props:children:1:props:params"
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
e:{"metadata":[],"error":null,"digest":"$undefined"}
13:"$e:metadata"
