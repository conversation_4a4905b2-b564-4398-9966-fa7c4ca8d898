(()=>{var a={};a.id=194,a.ids=[194],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1739:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>G});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(68988),i=c(39390),j=c(59821),k=c(63974),l=c(96752),m=c(30920),n=c(2041),o=c(85168),p=c(27747),q=c(9920),r=c(90812),s=c(53892),t=c(69206),u=c(25679),v=c(37826),w=c(73320),x=c(96474),y=c(41862),z=c(99270),A=c(13861),B=c(62688);let C=(0,B.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),D=(0,B.A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]]);var E=c(59556),F=c(71702);function G(){let[a,b]=(0,e.useState)(!1),[c,B]=(0,e.useState)(),[G,H]=(0,e.useState)(""),[I,J]=(0,e.useState)("all"),[K,L]=(0,e.useState)(!0),[M,N]=(0,e.useState)([]),[O,P]=(0,e.useState)([]),[Q,R]=(0,e.useState)({byMonth:[],byStatus:[],byChannel:[]}),{toast:S}=(0,F.dj)(),T=async()=>{try{L(!0);let[a,b,c,d,e]=await Promise.all([E.s6.getAll(),E.$H.getAll(),E.s6.getByMonth(),E.s6.getByStatus(),E.s6.getByChannel()]);N(a),P(b),R({byMonth:c,byStatus:d,byChannel:e})}catch(a){console.error("Error loading data:",a),S({title:"Error",description:"No se pudieron cargar los datos. Verifique que el backend est\xe9 ejecut\xe1ndose.",variant:"destructive"}),N([]),P([]),R({byMonth:[],byStatus:[],byChannel:[]})}finally{L(!1)}},[U,V]=(0,e.useState)({guestName:"",guestEmail:"",guestPhone:"",guests:"",checkInDate:"",checkOutDate:"",roomId:"",totalAmount:""}),W={reservations:{label:"Reservas"},cancellations:{label:"Cancelaciones"},confirmed:{label:"Confirmadas"},pending:{label:"Pendientes"},checkin:{label:"Check-in"},cancelled:{label:"Canceladas"}},X=async()=>{if(U.guestName&&U.guestEmail&&U.checkInDate&&U.checkOutDate&&U.roomId)try{let a=O.find(a=>a.id===parseInt(U.roomId));if(!a)return void S({title:"Error",description:"Habitaci\xf3n no encontrada",variant:"destructive"});let b=new Date(U.checkInDate),c=new Date(U.checkOutDate),d=Math.ceil((c.getTime()-b.getTime())/864e5),e=a.price*d,f={guestName:U.guestName,guestEmail:U.guestEmail,guestPhone:U.guestPhone||void 0,checkInDate:U.checkInDate,checkOutDate:U.checkOutDate,nights:d,guests:parseInt(U.guests)||1,roomId:parseInt(U.roomId),totalAmount:e,status:"PENDING",channel:"DIRECT"},g=await E.s6.create(f);N([g,...M]),V({guestName:"",guestEmail:"",guestPhone:"",guests:"",checkInDate:"",checkOutDate:"",roomId:"",totalAmount:""}),S({title:"\xc9xito",description:"Reserva creada exitosamente"}),T()}catch(a){console.error("Error creating reservation:",a),S({title:"Error",description:"No se pudo crear la reserva",variant:"destructive"})}else S({title:"Error",description:"Por favor complete todos los campos requeridos",variant:"destructive"})},Y=async a=>{if(confirm("\xbfEst\xe1 seguro de que desea eliminar esta reserva?"))try{await E.s6.delete(a),N(M.filter(b=>b.id!==a)),S({title:"\xc9xito",description:"Reserva eliminada exitosamente"}),T()}catch(a){console.error("Error deleting reservation:",a),S({title:"Error",description:"No se pudo eliminar la reserva",variant:"destructive"})}},Z=a=>{switch(a){case"INDIVIDUAL":return"Individual";case"DOBLE":return"Doble";case"SUITE":return"Suite";case"FAMILIAR":return"Familiar";default:return a}},$=M.filter(a=>{let b=a.guestName.toLowerCase().includes(G.toLowerCase())||a.id.toString().toLowerCase().includes(G.toLowerCase()),c="all"===I||a.status===I;return b&&c});return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(w.Qp,{children:(0,d.jsxs)(w.AB,{children:[(0,d.jsx)(w.J5,{children:(0,d.jsx)(w.w1,{href:"/",children:"Dashboard"})}),(0,d.jsx)(w.tH,{}),(0,d.jsx)(w.J5,{children:(0,d.jsx)(w.tJ,{children:"Reservas"})})]})}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Gesti\xf3n de Reservas"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Administra las reservations del hotel"})]}),(0,d.jsxs)(v.lG,{children:[(0,d.jsx)(v.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{children:[(0,d.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Nueva Reserva"]})}),(0,d.jsxs)(v.Cf,{className:"max-w-2xl",children:[(0,d.jsxs)(v.c7,{children:[(0,d.jsx)(v.L3,{children:"Nueva Reserva"}),(0,d.jsx)(v.rr,{children:"Crear una nueva reserva para el hotel"})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"guestName",children:"Nombre del Hu\xe9sped"}),(0,d.jsx)(h.p,{id:"guestName",placeholder:"Nombre completo",value:U.guestName,onChange:a=>V({...U,guestName:a.target.value})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"guestEmail",children:"Email"}),(0,d.jsx)(h.p,{id:"guestEmail",type:"email",placeholder:"<EMAIL>",value:U.guestEmail,onChange:a=>V({...U,guestEmail:a.target.value})})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"guestPhone",children:"Tel\xe9fono"}),(0,d.jsx)(h.p,{id:"guestPhone",placeholder:"+57 ************",value:U.guestPhone,onChange:a=>V({...U,guestPhone:a.target.value})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"guests",children:"N\xfamero de Hu\xe9spedes"}),(0,d.jsxs)(k.l6,{value:U.guests,onValueChange:a=>V({...U,guests:a}),children:[(0,d.jsx)(k.bq,{children:(0,d.jsx)(k.yv,{placeholder:"Seleccionar"})}),(0,d.jsxs)(k.gC,{children:[(0,d.jsx)(k.eb,{value:"1",children:"1 Hu\xe9sped"}),(0,d.jsx)(k.eb,{value:"2",children:"2 Hu\xe9spedes"}),(0,d.jsx)(k.eb,{value:"3",children:"3 Hu\xe9spedes"}),(0,d.jsx)(k.eb,{value:"4",children:"4 Hu\xe9spedes"}),(0,d.jsx)(k.eb,{value:"5",children:"5 Hu\xe9spedes"}),(0,d.jsx)(k.eb,{value:"6",children:"6 Hu\xe9spedes"})]})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{children:"Fecha de Entrada"}),(0,d.jsx)(h.p,{type:"date",value:U.checkInDate,onChange:a=>V({...U,checkInDate:a.target.value})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{children:"Fecha de Salida"}),(0,d.jsx)(h.p,{type:"date",value:U.checkOutDate,onChange:a=>V({...U,checkOutDate:a.target.value})})]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 gap-4",children:(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"roomId",children:"Habitaci\xf3n"}),(0,d.jsxs)(k.l6,{value:U.roomId,onValueChange:a=>V({...U,roomId:a}),children:[(0,d.jsx)(k.bq,{children:(0,d.jsx)(k.yv,{placeholder:"Seleccionar habitaci\xf3n"})}),(0,d.jsx)(k.gC,{children:O.filter(a=>a.isAvailable).map(a=>(0,d.jsxs)(k.eb,{value:a.id.toString(),children:[a.number," - ",Z(a.type)," ($",a.price.toLocaleString(),"/noche)"]},a.id))})]})]})}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(v.zM,{asChild:!0,children:(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"})}),(0,d.jsxs)(g.$,{onClick:X,disabled:K,children:[K&&(0,d.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Crear Reserva"]})]})]})]})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsx)(f.aR,{children:(0,d.jsx)(f.ZB,{children:"Filtros"})}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(z.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(h.p,{placeholder:"Buscar por nombre o ID de reserva...",value:G,onChange:a=>H(a.target.value),className:"pl-8"})]})}),(0,d.jsxs)(k.l6,{value:I,onValueChange:J,children:[(0,d.jsx)(k.bq,{className:"w-48",children:(0,d.jsx)(k.yv,{placeholder:"Estado"})}),(0,d.jsxs)(k.gC,{children:[(0,d.jsx)(k.eb,{value:"all",children:"Todos los estados"}),(0,d.jsx)(k.eb,{value:"CONFIRMED",children:"Confirmada"}),(0,d.jsx)(k.eb,{value:"PENDING",children:"Pendiente"}),(0,d.jsx)(k.eb,{value:"CHECKED_IN",children:"Check-in"}),(0,d.jsx)(k.eb,{value:"CHECKED_OUT",children:"Check-out"}),(0,d.jsx)(k.eb,{value:"CANCELLED",children:"Cancelada"})]})]})]})})]}),a&&!K&&(0,d.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,d.jsxs)(f.Zp,{className:"lg:col-span-2",children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Tendencia de Reservas"}),(0,d.jsx)(f.BT,{children:"Reservas y cancelaciones por mes"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)(m.at,{config:W,className:"h-[300px]",children:(0,d.jsxs)(n.E,{data:Q.byMonth,children:[(0,d.jsx)(o.d,{strokeDasharray:"3 3"}),(0,d.jsx)(p.W,{dataKey:"month"}),(0,d.jsx)(q.h,{}),(0,d.jsx)(m.II,{content:(0,d.jsx)(m.Nt,{})}),(0,d.jsx)(r.y,{dataKey:"reservations",fill:"hsl(var(--chart-1))"}),(0,d.jsx)(r.y,{dataKey:"cancellations",fill:"hsl(var(--chart-4))"})]})})})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Estado de Reservas"}),(0,d.jsx)(f.BT,{children:"Distribuci\xf3n actual"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)(m.at,{config:W,className:"mx-auto aspect-square max-h-[250px]",children:(0,d.jsxs)(s.r,{children:[(0,d.jsx)(m.II,{cursor:!1,content:(0,d.jsx)(m.Nt,{hideLabel:!0})}),(0,d.jsx)(t.F,{data:Q.byStatus,dataKey:"value",nameKey:"name",innerRadius:50,strokeWidth:5,children:Q.byStatus.map((a,b)=>(0,d.jsx)(u.f,{fill:a.fill},`cell-${b}`))})]})}),(0,d.jsx)("div",{className:"flex flex-wrap justify-center gap-2 mt-4",children:Q.byStatus.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:a.fill}}),(0,d.jsxs)("span",{className:"text-xs",children:[a.name," (",a.value,"%)"]})]},b))})]})]}),(0,d.jsxs)(f.Zp,{className:"lg:col-span-3",children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Canales de Reserva"}),(0,d.jsx)(f.BT,{children:"Reservas por canal de distribuci\xf3n"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)(m.at,{config:W,className:"h-[200px]",children:(0,d.jsxs)(n.E,{data:Q.byChannel,layout:"horizontal",children:[(0,d.jsx)(o.d,{strokeDasharray:"3 3"}),(0,d.jsx)(p.W,{type:"number"}),(0,d.jsx)(q.h,{dataKey:"channel",type:"category",width:80}),(0,d.jsx)(m.II,{content:(0,d.jsx)(m.Nt,{})}),(0,d.jsx)(r.y,{dataKey:"reservations",fill:"hsl(var(--chart-2))"})]})})})]})]}),K&&(0,d.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,d.jsx)(y.A,{className:"h-8 w-8 animate-spin"}),(0,d.jsx)("span",{className:"ml-2",children:"Cargando datos..."})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Lista de Reservas"}),(0,d.jsxs)(f.BT,{children:[$.length," reservas encontradas"]})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"ID"}),(0,d.jsx)(l.nd,{children:"Hu\xe9sped"}),(0,d.jsx)(l.nd,{children:"Habitaci\xf3n"}),(0,d.jsx)(l.nd,{children:"Fechas"}),(0,d.jsx)(l.nd,{children:"Hu\xe9spedes"}),(0,d.jsx)(l.nd,{children:"Total"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Canal"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:$.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsxs)(l.nA,{className:"font-medium",children:["#",a.id]}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.guestName}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.guestEmail})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.room.number}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:Z(a.room.type)})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsx)("div",{children:new Date(a.checkInDate).toLocaleDateString()}),(0,d.jsx)("div",{children:new Date(a.checkOutDate).toLocaleDateString()}),(0,d.jsxs)("div",{className:"text-muted-foreground",children:[a.nights," noches"]})]})}),(0,d.jsx)(l.nA,{children:a.guests}),(0,d.jsxs)(l.nA,{children:["$",a.totalAmount.toLocaleString()]}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"CONFIRMED":return(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Confirmada"});case"PENDING":return(0,d.jsx)(j.E,{className:"bg-yellow-100 text-yellow-800",children:"Pendiente"});case"CHECKED_IN":return(0,d.jsx)(j.E,{className:"bg-blue-100 text-blue-800",children:"Check-in"});case"CHECKED_OUT":return(0,d.jsx)(j.E,{className:"bg-gray-100 text-gray-800",children:"Check-out"});case"CANCELLED":return(0,d.jsx)(j.E,{className:"bg-red-100 text-red-800",children:"Cancelada"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(a.status)}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"DIRECT":return"Directo";case"BOOKING_COM":return"Booking.com";case"EXPEDIA":return"Expedia";case"AIRBNB":return"Airbnb";case"AGENCY":return"Agencia";case"PHONE":return"Tel\xe9fono";default:return a}})(a.channel)}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(g.$,{variant:"ghost",size:"icon",onClick:()=>(a=>{let b=M.find(b=>b.id===a);b&&alert(`Detalles de la reserva ${b.id}:
Hu\xe9sped: ${b.guestName}
Habitaci\xf3n: ${b.room.number}
Fechas: ${b.checkInDate} - ${b.checkOutDate}`)})(a.id),children:(0,d.jsx)(A.A,{className:"h-4 w-4"})}),(0,d.jsx)(g.$,{variant:"ghost",size:"icon",onClick:()=>{var b;return b=a.id,void console.log(`Editando reserva ${b}`)},children:(0,d.jsx)(C,{className:"h-4 w-4"})}),(0,d.jsx)(g.$,{variant:"ghost",size:"icon",onClick:()=>Y(a.id),children:(0,d.jsx)(D,{className:"h-4 w-4"})})]})})]},a.id))})]})})]})]})]})}},2041:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(92491),e=c(90812),f=c(27747),g=c(9920),h=c(84629),i=(0,d.gu)({chartName:"BarChart",GraphicalChild:e.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:f.W},{axisType:"yAxis",AxisComp:g.h}],formatAxisMap:h.pr})},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3594:(a,b,c)=>{"use strict";function d(){return null}c.r(b),c.d(b,{default:()=>d})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19266:(a,b,c)=>{Promise.resolve().then(c.bind(c,68249))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},37418:(a,b,c)=>{Promise.resolve().then(c.bind(c,1739))},37826:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>m,L3:()=>o,c7:()=>n,lG:()=>i,rr:()=>p,zM:()=>j});var d=c(60687),e=c(43210),f=c(26134),g=c(11860),h=c(96241);let i=f.bL,j=f.l9,k=f.ZL;f.bm;let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hJ,{ref:c,className:(0,h.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...b}));l.displayName=f.hJ.displayName;let m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(k,{children:[(0,d.jsx)(l,{}),(0,d.jsxs)(f.UC,{ref:e,className:(0,h.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[b,(0,d.jsxs)(f.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=f.UC.displayName;let n=({className:a,...b})=>(0,d.jsx)("div",{className:(0,h.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...b});n.displayName="DialogHeader";let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hE,{ref:c,className:(0,h.cn)("text-lg font-semibold leading-none tracking-tight",a),...b}));o.displayName=f.hE.displayName;let p=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.VY,{ref:c,className:(0,h.cn)("text-sm text-muted-foreground",a),...b}));p.displayName=f.VY.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48624:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["reservas",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,68249)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\reservas\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(c.bind(c,3594)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\reservas\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\reservas\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/reservas/page",pathname:"/reservas",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/reservas/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68249:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\reservas\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\reservas\\page.tsx","default")},73320:(a,b,c)=>{"use strict";c.d(b,{Qp:()=>i,J5:()=>k,w1:()=>l,AB:()=>j,tJ:()=>m,tH:()=>n});var d=c(60687),e=c(43210),f=c(8730),g=c(14952);(0,c(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);var h=c(96241);let i=e.forwardRef(({...a},b)=>(0,d.jsx)("nav",{ref:b,"aria-label":"breadcrumb",...a}));i.displayName="Breadcrumb";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("ol",{ref:c,className:(0,h.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a),...b}));j.displayName="BreadcrumbList";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("li",{ref:c,className:(0,h.cn)("inline-flex items-center gap-1.5",a),...b}));k.displayName="BreadcrumbItem";let l=e.forwardRef(({asChild:a,className:b,...c},e)=>{let g=a?f.DX:"a";return(0,d.jsx)(g,{ref:e,className:(0,h.cn)("transition-colors hover:text-foreground",b),...c})});l.displayName="BreadcrumbLink";let m=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("span",{ref:c,role:"link","aria-disabled":"true","aria-current":"page",className:(0,h.cn)("font-normal text-foreground",a),...b}));m.displayName="BreadcrumbPage";let n=({children:a,className:b,...c})=>(0,d.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,h.cn)("[&>svg]:w-3.5 [&>svg]:h-3.5",b),...c,children:a??(0,d.jsx)(g.A,{})});n.displayName="BreadcrumbSeparator"},85168:(a,b,c)=>{"use strict";c.d(b,{d:()=>E});var d=c(43210),e=c.n(d),f=c(5231),g=c.n(f),h=c(10521),i=c(22989),j=c(54186),k=c(30087),l=c(35261),m=c(34005),n=c(277),o=["x1","y1","x2","y2","key"],p=["offset"];function q(a){return(q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function r(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function s(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?r(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=q(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=q(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==q(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):r(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function t(){return(t=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function u(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var v=function(a){var b=a.fill;if(!b||"none"===b)return null;var c=a.fillOpacity,d=a.x,f=a.y,g=a.width,h=a.height,i=a.ry;return e().createElement("rect",{x:d,y:f,ry:i,width:g,height:h,stroke:"none",fill:b,fillOpacity:c,className:"recharts-cartesian-grid-bg"})};function w(a,b){var c;if(e().isValidElement(a))c=e().cloneElement(a,b);else if(g()(a))c=a(b);else{var d=b.x1,f=b.y1,h=b.x2,i=b.y2,k=b.key,l=u(b,o),m=(0,j.J9)(l,!1),n=(m.offset,u(m,p));c=e().createElement("line",t({},n,{x1:d,y1:f,x2:h,y2:i,fill:"none",key:k}))}return c}function x(a){var b=a.x,c=a.width,d=a.horizontal,f=void 0===d||d,g=a.horizontalPoints;if(!f||!g||!g.length)return null;var h=g.map(function(d,e){return w(f,s(s({},a),{},{x1:b,y1:d,x2:b+c,y2:d,key:"line-".concat(e),index:e}))});return e().createElement("g",{className:"recharts-cartesian-grid-horizontal"},h)}function y(a){var b=a.y,c=a.height,d=a.vertical,f=void 0===d||d,g=a.verticalPoints;if(!f||!g||!g.length)return null;var h=g.map(function(d,e){return w(f,s(s({},a),{},{x1:d,y1:b,x2:d,y2:b+c,key:"line-".concat(e),index:e}))});return e().createElement("g",{className:"recharts-cartesian-grid-vertical"},h)}function z(a){var b=a.horizontalFill,c=a.fillOpacity,d=a.x,f=a.y,g=a.width,h=a.height,i=a.horizontalPoints,j=a.horizontal;if(!(void 0===j||j)||!b||!b.length)return null;var k=i.map(function(a){return Math.round(a+f-f)}).sort(function(a,b){return a-b});f!==k[0]&&k.unshift(0);var l=k.map(function(a,i){var j=k[i+1]?k[i+1]-a:f+h-a;if(j<=0)return null;var l=i%b.length;return e().createElement("rect",{key:"react-".concat(i),y:a,x:d,height:j,width:g,stroke:"none",fill:b[l],fillOpacity:c,className:"recharts-cartesian-grid-bg"})});return e().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function A(a){var b=a.vertical,c=a.verticalFill,d=a.fillOpacity,f=a.x,g=a.y,h=a.width,i=a.height,j=a.verticalPoints;if(!(void 0===b||b)||!c||!c.length)return null;var k=j.map(function(a){return Math.round(a+f-f)}).sort(function(a,b){return a-b});f!==k[0]&&k.unshift(0);var l=k.map(function(a,b){var j=k[b+1]?k[b+1]-a:f+h-a;if(j<=0)return null;var l=b%c.length;return e().createElement("rect",{key:"react-".concat(b),x:a,y:g,width:j,height:i,stroke:"none",fill:c[l],fillOpacity:d,className:"recharts-cartesian-grid-bg"})});return e().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var B=function(a,b){var c=a.xAxis,d=a.width,e=a.height,f=a.offset;return(0,k.PW)((0,l.f)(s(s(s({},m.u.defaultProps),c),{},{ticks:(0,k.Rh)(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.left,f.left+f.width,b)},C=function(a,b){var c=a.yAxis,d=a.width,e=a.height,f=a.offset;return(0,k.PW)((0,l.f)(s(s(s({},m.u.defaultProps),c),{},{ticks:(0,k.Rh)(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.top,f.top+f.height,b)},D={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function E(a){var b,c,d,f,j,k,l=(0,n.yi)(),m=(0,n.rY)(),o=(0,n.hj)(),p=s(s({},a),{},{stroke:null!=(b=a.stroke)?b:D.stroke,fill:null!=(c=a.fill)?c:D.fill,horizontal:null!=(d=a.horizontal)?d:D.horizontal,horizontalFill:null!=(f=a.horizontalFill)?f:D.horizontalFill,vertical:null!=(j=a.vertical)?j:D.vertical,verticalFill:null!=(k=a.verticalFill)?k:D.verticalFill,x:(0,i.Et)(a.x)?a.x:o.left,y:(0,i.Et)(a.y)?a.y:o.top,width:(0,i.Et)(a.width)?a.width:o.width,height:(0,i.Et)(a.height)?a.height:o.height}),r=p.x,u=p.y,w=p.width,E=p.height,F=p.syncWithTicks,G=p.horizontalValues,H=p.verticalValues,I=(0,n.pj)(),J=(0,n.$G)();if(!(0,i.Et)(w)||w<=0||!(0,i.Et)(E)||E<=0||!(0,i.Et)(r)||r!==+r||!(0,i.Et)(u)||u!==+u)return null;var K=p.verticalCoordinatesGenerator||B,L=p.horizontalCoordinatesGenerator||C,M=p.horizontalPoints,N=p.verticalPoints;if((!M||!M.length)&&g()(L)){var O=G&&G.length,P=L({yAxis:J?s(s({},J),{},{ticks:O?G:J.ticks}):void 0,width:l,height:m,offset:o},!!O||F);(0,h.R)(Array.isArray(P),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(q(P),"]")),Array.isArray(P)&&(M=P)}if((!N||!N.length)&&g()(K)){var Q=H&&H.length,R=K({xAxis:I?s(s({},I),{},{ticks:Q?H:I.ticks}):void 0,width:l,height:m,offset:o},!!Q||F);(0,h.R)(Array.isArray(R),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(q(R),"]")),Array.isArray(R)&&(N=R)}return e().createElement("g",{className:"recharts-cartesian-grid"},e().createElement(v,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),e().createElement(x,t({},p,{offset:o,horizontalPoints:M,xAxis:I,yAxis:J})),e().createElement(y,t({},p,{offset:o,verticalPoints:N,xAxis:I,yAxis:J})),e().createElement(z,t({},p,{horizontalPoints:M})),e().createElement(A,t({},p,{verticalPoints:N})))}E.displayName="CartesianGrid"},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[438,271,436,957,274],()=>b(b.s=48624));module.exports=c})();