(()=>{var a={};a.id=800,a.ids=[800],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25177:(a,b,c)=>{"use strict";c.d(b,{C1:()=>v,bL:()=>u});var d=c(43210),e=c(11273),f=c(14163),g=c(60687),h="Progress",[i,j]=(0,e.A)(h),[k,l]=i(h),m=d.forwardRef((a,b)=>{var c,d;let{__scopeProgress:e,value:h=null,max:i,getValueLabel:j=p,...l}=a;(i||0===i)&&!s(i)&&console.error((c=`${i}`,`Invalid prop \`max\` of value \`${c}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=s(i)?i:100;null===h||t(h,m)||console.error((d=`${h}`,`Invalid prop \`value\` of value \`${d}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let n=t(h,m)?h:null,o=r(n)?j(n,m):void 0;return(0,g.jsx)(k,{scope:e,value:n,max:m,children:(0,g.jsx)(f.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":r(n)?n:void 0,"aria-valuetext":o,role:"progressbar","data-state":q(n,m),"data-value":n??void 0,"data-max":m,...l,ref:b})})});m.displayName=h;var n="ProgressIndicator",o=d.forwardRef((a,b)=>{let{__scopeProgress:c,...d}=a,e=l(n,c);return(0,g.jsx)(f.sG.div,{"data-state":q(e.value,e.max),"data-value":e.value??void 0,"data-max":e.max,...d,ref:b})});function p(a,b){return`${Math.round(a/b*100)}%`}function q(a,b){return null==a?"indeterminate":a===b?"complete":"loading"}function r(a){return"number"==typeof a}function s(a){return r(a)&&!isNaN(a)&&a>0}function t(a,b){return r(a)&&!isNaN(a)&&a<=b&&a>=0}o.displayName=n;var u=m,v=o},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35984:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(37413),e=c(54781),f=c(51358);function g(){return(0,d.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(e.E,{className:"h-8 w-48"}),(0,d.jsx)(e.E,{className:"h-4 w-96"})]}),(0,d.jsx)(e.E,{className:"h-10 w-32"})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(e.E,{className:"h-10 w-full"}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(e.E,{className:"h-6 w-48"}),(0,d.jsx)(e.E,{className:"h-4 w-96"})]}),(0,d.jsx)(f.Wu,{className:"space-y-6",children:(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:Array.from({length:8}).map((a,b)=>(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(e.E,{className:"h-4 w-24"}),(0,d.jsx)(e.E,{className:"h-10 w-full"})]},b))})})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(e.E,{className:"h-6 w-48"}),(0,d.jsx)(e.E,{className:"h-4 w-96"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:Array.from({length:3}).map((a,b)=>(0,d.jsxs)("div",{className:"p-4 border rounded-lg text-center space-y-2",children:[(0,d.jsx)(e.E,{className:"h-8 w-16 mx-auto"}),(0,d.jsx)(e.E,{className:"h-4 w-24 mx-auto"})]},b))})})]})]})]})}},36811:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\configuracion\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\configuracion\\page.tsx","default")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43649:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},59034:(a,b,c)=>{Promise.resolve().then(c.bind(c,99863))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69650:(a,b,c)=>{Promise.resolve().then(c.bind(c,36811))},73320:(a,b,c)=>{"use strict";c.d(b,{Qp:()=>i,J5:()=>k,w1:()=>l,AB:()=>j,tJ:()=>m,tH:()=>n});var d=c(60687),e=c(43210),f=c(8730),g=c(14952);(0,c(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);var h=c(96241);let i=e.forwardRef(({...a},b)=>(0,d.jsx)("nav",{ref:b,"aria-label":"breadcrumb",...a}));i.displayName="Breadcrumb";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("ol",{ref:c,className:(0,h.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a),...b}));j.displayName="BreadcrumbList";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("li",{ref:c,className:(0,h.cn)("inline-flex items-center gap-1.5",a),...b}));k.displayName="BreadcrumbItem";let l=e.forwardRef(({asChild:a,className:b,...c},e)=>{let g=a?f.DX:"a";return(0,d.jsx)(g,{ref:e,className:(0,h.cn)("transition-colors hover:text-foreground",b),...c})});l.displayName="BreadcrumbLink";let m=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("span",{ref:c,role:"link","aria-disabled":"true","aria-current":"page",className:(0,h.cn)("font-normal text-foreground",a),...b}));m.displayName="BreadcrumbPage";let n=({children:a,className:b,...c})=>(0,d.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,h.cn)("[&>svg]:w-3.5 [&>svg]:h-3.5",b),...c,children:a??(0,d.jsx)(g.A,{})});n.displayName="BreadcrumbSeparator"},85778:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86287:(a,b,c)=>{"use strict";c.d(b,{k:()=>h});var d=c(60687),e=c(43210),f=c(25177),g=c(96241);let h=e.forwardRef(({className:a,value:b,...c},e)=>(0,d.jsx)(f.bL,{ref:e,className:(0,g.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...c,children:(0,d.jsx)(f.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(b||0)}%)`}})}));h.displayName=f.bL.displayName},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91114:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["configuracion",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,36811)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\configuracion\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(c.bind(c,35984)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\configuracion\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\configuracion\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/configuracion/page",pathname:"/configuracion",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/configuracion/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},99863:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>K});var d=c(60687),e=c(43210),f=c(24934),g=c(68988),h=c(39390),i=c(15616),j=c(55192),k=c(85910),l=c(59821),m=c(40214),n=c(63974),o=c(86287),p=c(73320),q=c(62688);let r=(0,q.A)("hotel",[["path",{d:"M10 22v-6.57",key:"1wmca3"}],["path",{d:"M12 11h.01",key:"z322tv"}],["path",{d:"M12 7h.01",key:"1ivr5q"}],["path",{d:"M14 15.43V22",key:"1q2vjd"}],["path",{d:"M15 16a5 5 0 0 0-6 0",key:"o9wqvi"}],["path",{d:"M16 11h.01",key:"xkw8gn"}],["path",{d:"M16 7h.01",key:"1kdx03"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 7h.01",key:"1vti4s"}],["rect",{x:"4",y:"2",width:"16",height:"20",rx:"2",key:"1uxh74"}]]);var s=c(41550),t=c(48340);let u=(0,q.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var v=c(97992),w=c(48730),x=c(23928);let y=(0,q.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var z=c(8819);let A=(0,q.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var B=c(97051),C=c(5336),D=c(84027),E=c(85778),F=c(58887),G=c(40228),H=c(99891),I=c(43649),J=c(71702);function K(){let[a,b]=(0,e.useState)(!1),[c,q]=(0,e.useState)({name:"Hotel Craft Suite",description:"Un hotel de lujo en el coraz\xf3n de la ciudad",address:"123 Hotel Street, Ciudad, Pa\xeds",phone:"+****************",email:"<EMAIL>",website:"www.hotelcraft.com",checkInTime:"15:00",checkOutTime:"11:00",currency:"MXN",language:"es",timezone:"America/Mexico_City",taxRate:"16"}),[K,L]=(0,e.useState)({autoBackup:!0,backupFrequency:"daily",emailNotifications:!0,smsNotifications:!1,maintenanceMode:!1,debugMode:!1,apiAccess:!0,maxLoginAttempts:"5",sessionTimeout:"30"}),[M,N]=(0,e.useState)({stripe:{enabled:!0,status:"connected"},sendgrid:{enabled:!0,status:"connected"},twilio:{enabled:!1,status:"disconnected"},booking:{enabled:!0,status:"connected"},expedia:{enabled:!1,status:"disconnected"}}),O=async()=>{b(!0),await new Promise(a=>setTimeout(a,1e3)),b(!1),(0,J.oR)({title:"Configuraci\xf3n guardada",description:"La configuraci\xf3n del hotel se ha actualizado correctamente."})},P=async()=>{b(!0),await new Promise(a=>setTimeout(a,1e3)),b(!1),(0,J.oR)({title:"Configuraci\xf3n del sistema guardada",description:"Los cambios se han aplicado correctamente."})},Q=a=>{(0,J.oR)({title:`Probando ${a}`,description:"Verificando conexi\xf3n..."}),setTimeout(()=>{(0,J.oR)({title:"Conexi\xf3n exitosa",description:`La integraci\xf3n con ${a} est\xe1 funcionando correctamente.`})},2e3)};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(p.Qp,{children:(0,d.jsxs)(p.AB,{children:[(0,d.jsx)(p.J5,{children:(0,d.jsx)(p.w1,{href:"/",children:"Dashboard"})}),(0,d.jsx)(p.tH,{}),(0,d.jsx)(p.J5,{children:(0,d.jsx)(p.tJ,{children:"Configuraci\xf3n"})})]})}),(0,d.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Configuraci\xf3n del Sistema"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Gestiona la configuraci\xf3n general del hotel y sistema"})]})}),(0,d.jsxs)(k.tU,{defaultValue:"hotel",className:"space-y-4",children:[(0,d.jsxs)(k.j7,{className:"grid w-full grid-cols-5",children:[(0,d.jsx)(k.Xi,{value:"hotel",children:"Hotel"}),(0,d.jsx)(k.Xi,{value:"system",children:"Sistema"}),(0,d.jsx)(k.Xi,{value:"integrations",children:"Integraciones"}),(0,d.jsx)(k.Xi,{value:"security",children:"Seguridad"}),(0,d.jsx)(k.Xi,{value:"advanced",children:"Avanzado"})]}),(0,d.jsx)(k.av,{value:"hotel",className:"space-y-4",children:(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(r,{className:"h-5 w-5"}),"Informaci\xf3n del Hotel"]}),(0,d.jsx)(j.BT,{children:"Configuraci\xf3n b\xe1sica de la informaci\xf3n del hotel"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"hotelName",children:"Nombre del Hotel"}),(0,d.jsx)(g.p,{id:"hotelName",value:c.name,onChange:a=>q({...c,name:a.target.value})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"hotelEmail",children:"Email de Contacto"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(s.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"hotelEmail",type:"email",value:c.email,onChange:a=>q({...c,email:a.target.value}),className:"pl-8"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"hotelPhone",children:"Tel\xe9fono"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(t.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"hotelPhone",value:c.phone,onChange:a=>q({...c,phone:a.target.value}),className:"pl-8"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"hotelWebsite",children:"Sitio Web"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(u,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"hotelWebsite",value:c.website,onChange:a=>q({...c,website:a.target.value}),className:"pl-8"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"hotelAddress",children:"Direcci\xf3n"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(v.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"hotelAddress",value:c.address,onChange:a=>q({...c,address:a.target.value}),className:"pl-8"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"hotelDescription",children:"Descripci\xf3n"}),(0,d.jsx)(i.T,{id:"hotelDescription",value:c.description,onChange:a=>q({...c,description:a.target.value}),rows:3})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"checkInTime",children:"Hora de Check-in"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(w.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"checkInTime",type:"time",value:c.checkInTime,onChange:a=>q({...c,checkInTime:a.target.value}),className:"pl-8"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"checkOutTime",children:"Hora de Check-out"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(w.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"checkOutTime",type:"time",value:c.checkOutTime,onChange:a=>q({...c,checkOutTime:a.target.value}),className:"pl-8"})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"currency",children:"Moneda"}),(0,d.jsxs)(n.l6,{value:c.currency,onValueChange:a=>q({...c,currency:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"MXN",children:"Peso Mexicano (MXN)"}),(0,d.jsx)(n.eb,{value:"USD",children:"D\xf3lar Americano (USD)"}),(0,d.jsx)(n.eb,{value:"EUR",children:"Euro (EUR)"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"language",children:"Idioma"}),(0,d.jsxs)(n.l6,{value:c.language,onValueChange:a=>q({...c,language:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"es",children:"Espa\xf1ol"}),(0,d.jsx)(n.eb,{value:"en",children:"English"}),(0,d.jsx)(n.eb,{value:"fr",children:"Fran\xe7ais"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"taxRate",children:"Tasa de Impuesto (%)"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(x.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"taxRate",value:c.taxRate,onChange:a=>q({...c,taxRate:a.target.value}),className:"pl-8"})]})]})]}),(0,d.jsx)("div",{className:"flex justify-end",children:(0,d.jsx)(f.$,{onClick:O,disabled:a,children:a?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(y,{className:"mr-2 h-4 w-4 animate-spin"}),"Guardando..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(z.A,{className:"mr-2 h-4 w-4"}),"Guardar Configuraci\xf3n"]})})})]})]})}),(0,d.jsxs)(k.av,{value:"system",className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)(j.Zp,{children:[(0,d.jsx)(j.aR,{children:(0,d.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(A,{className:"h-5 w-5"}),"Respaldos Autom\xe1ticos"]})}),(0,d.jsxs)(j.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(h.J,{children:"Respaldos Autom\xe1ticos"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Crear respaldos autom\xe1ticos de la base de datos"})]}),(0,d.jsx)(m.d,{checked:K.autoBackup,onCheckedChange:a=>L({...K,autoBackup:a})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{children:"Frecuencia de Respaldo"}),(0,d.jsxs)(n.l6,{value:K.backupFrequency,onValueChange:a=>L({...K,backupFrequency:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"hourly",children:"Cada hora"}),(0,d.jsx)(n.eb,{value:"daily",children:"Diario"}),(0,d.jsx)(n.eb,{value:"weekly",children:"Semanal"}),(0,d.jsx)(n.eb,{value:"monthly",children:"Mensual"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{children:"\xdaltimo Respaldo"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Hace 2 horas - Exitoso"}),(0,d.jsx)(o.k,{value:100,className:"h-2"})]})]})]}),(0,d.jsxs)(j.Zp,{children:[(0,d.jsx)(j.aR,{children:(0,d.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(B.A,{className:"h-5 w-5"}),"Notificaciones del Sistema"]})}),(0,d.jsxs)(j.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(h.J,{children:"Notificaciones por Email"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Alertas del sistema por correo"})]}),(0,d.jsx)(m.d,{checked:K.emailNotifications,onCheckedChange:a=>L({...K,emailNotifications:a})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(h.J,{children:"Notificaciones SMS"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Alertas cr\xedticas por SMS"})]}),(0,d.jsx)(m.d,{checked:K.smsNotifications,onCheckedChange:a=>L({...K,smsNotifications:a})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{children:"Estado del Sistema"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(C.A,{className:"h-4 w-4 text-green-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Todos los servicios operativos"})]})]})]})]})]}),(0,d.jsxs)(j.Zp,{children:[(0,d.jsx)(j.aR,{children:(0,d.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(D.A,{className:"h-5 w-5"}),"Configuraci\xf3n Avanzada"]})}),(0,d.jsxs)(j.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(h.J,{children:"Modo de Mantenimiento"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Deshabilitar acceso temporal al sistema"})]}),(0,d.jsx)(m.d,{checked:K.maintenanceMode,onCheckedChange:a=>L({...K,maintenanceMode:a})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(h.J,{children:"Acceso a API"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Permitir conexiones externas via API"})]}),(0,d.jsx)(m.d,{checked:K.apiAccess,onCheckedChange:a=>L({...K,apiAccess:a})})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"maxLoginAttempts",children:"M\xe1ximo Intentos de Login"}),(0,d.jsx)(g.p,{id:"maxLoginAttempts",type:"number",value:K.maxLoginAttempts,onChange:a=>L({...K,maxLoginAttempts:a.target.value})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"sessionTimeout",children:"Timeout de Sesi\xf3n (minutos)"}),(0,d.jsx)(g.p,{id:"sessionTimeout",type:"number",value:K.sessionTimeout,onChange:a=>L({...K,sessionTimeout:a.target.value})})]})]}),(0,d.jsx)("div",{className:"flex justify-end",children:(0,d.jsx)(f.$,{onClick:P,disabled:a,children:a?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(y,{className:"mr-2 h-4 w-4 animate-spin"}),"Guardando..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(z.A,{className:"mr-2 h-4 w-4"}),"Guardar Configuraci\xf3n"]})})})]})]})]}),(0,d.jsx)(k.av,{value:"integrations",className:"space-y-4",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(E.A,{className:"h-5 w-5"}),"Stripe"]}),(0,d.jsx)(j.BT,{children:"Procesamiento de pagos con tarjeta"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:`h-2 w-2 rounded-full ${"connected"===M.stripe.status?"bg-green-500":"bg-red-500"}`}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"connected"===M.stripe.status?"Conectado":"Desconectado"})]}),(0,d.jsx)(m.d,{checked:M.stripe.enabled,onCheckedChange:a=>N({...M,stripe:{...M.stripe,enabled:a}})})]}),(0,d.jsx)(f.$,{variant:"outline",className:"w-full bg-transparent",onClick:()=>Q("Stripe"),children:"Probar Conexi\xf3n"})]})]}),(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(s.A,{className:"h-5 w-5"}),"SendGrid"]}),(0,d.jsx)(j.BT,{children:"Servicio de env\xedo de emails"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:`h-2 w-2 rounded-full ${"connected"===M.sendgrid.status?"bg-green-500":"bg-red-500"}`}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"connected"===M.sendgrid.status?"Conectado":"Desconectado"})]}),(0,d.jsx)(m.d,{checked:M.sendgrid.enabled,onCheckedChange:a=>N({...M,sendgrid:{...M.sendgrid,enabled:a}})})]}),(0,d.jsx)(f.$,{variant:"outline",className:"w-full bg-transparent",onClick:()=>Q("SendGrid"),children:"Probar Conexi\xf3n"})]})]}),(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(F.A,{className:"h-5 w-5"}),"Twilio"]}),(0,d.jsx)(j.BT,{children:"Servicio de mensajer\xeda SMS"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:`h-2 w-2 rounded-full ${"connected"===M.twilio.status?"bg-green-500":"bg-red-500"}`}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"connected"===M.twilio.status?"Conectado":"Desconectado"})]}),(0,d.jsx)(m.d,{checked:M.twilio.enabled,onCheckedChange:a=>N({...M,twilio:{...M.twilio,enabled:a}})})]}),(0,d.jsx)(f.$,{variant:"outline",className:"w-full bg-transparent",onClick:()=>Q("Twilio"),children:"Configurar"})]})]}),(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(G.A,{className:"h-5 w-5"}),"Booking.com"]}),(0,d.jsx)(j.BT,{children:"Sincronizaci\xf3n de reservas"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:`h-2 w-2 rounded-full ${"connected"===M.booking.status?"bg-green-500":"bg-red-500"}`}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"connected"===M.booking.status?"Conectado":"Desconectado"})]}),(0,d.jsx)(m.d,{checked:M.booking.enabled,onCheckedChange:a=>N({...M,booking:{...M.booking,enabled:a}})})]}),(0,d.jsx)(f.$,{variant:"outline",className:"w-full bg-transparent",onClick:()=>Q("Booking.com"),children:"Probar Conexi\xf3n"})]})]})]})}),(0,d.jsx)(k.av,{value:"security",className:"space-y-4",children:(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(H.A,{className:"h-5 w-5"}),"Pol\xedticas de Seguridad"]}),(0,d.jsx)(j.BT,{children:"Configuraci\xf3n de seguridad y pol\xedticas de acceso"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"font-medium",children:"Pol\xedticas de Contrase\xf1a"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{children:"Longitud M\xednima"}),(0,d.jsx)(g.p,{type:"number",defaultValue:"8"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{children:"Expiraci\xf3n (d\xedas)"}),(0,d.jsx)(g.p,{type:"number",defaultValue:"90"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",id:"requireUppercase",defaultChecked:!0}),(0,d.jsx)(h.J,{htmlFor:"requireUppercase",children:"Requerir may\xfasculas"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",id:"requireNumbers",defaultChecked:!0}),(0,d.jsx)(h.J,{htmlFor:"requireNumbers",children:"Requerir n\xfameros"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",id:"requireSymbols",defaultChecked:!0}),(0,d.jsx)(h.J,{htmlFor:"requireSymbols",children:"Requerir s\xedmbolos especiales"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"font-medium",children:"Configuraci\xf3n de Sesi\xf3n"}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(h.J,{children:"Modo de Mantenimiento"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Restringir acceso durante mantenimiento"})]}),(0,d.jsx)(m.d,{checked:K.maintenanceMode,onCheckedChange:a=>L({...K,maintenanceMode:a})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(h.J,{children:"Modo Debug"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Mostrar informaci\xf3n de depuraci\xf3n"})]}),(0,d.jsx)(m.d,{checked:K.debugMode,onCheckedChange:a=>L({...K,debugMode:a})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"font-medium",children:"Monitoreo de Seguridad"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(C.A,{className:"h-4 w-4 text-green-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:"Firewall Activo"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"\xdaltima actualizaci\xf3n: hace 1 hora"})]})]}),(0,d.jsx)(l.E,{variant:"secondary",children:"Activo"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(I.A,{className:"h-4 w-4 text-yellow-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:"Certificado SSL"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Expira en 30 d\xedas"})]})]}),(0,d.jsx)(l.E,{variant:"outline",children:"Renovar"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(C.A,{className:"h-4 w-4 text-green-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:"Detecci\xf3n de Intrusos"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Sin amenazas detectadas"})]})]}),(0,d.jsx)(l.E,{variant:"secondary",children:"Activo"})]})]})]})]})]})}),(0,d.jsx)(k.av,{value:"advanced",className:"space-y-4",children:(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(A,{className:"h-5 w-5"}),"Gesti\xf3n de Base de Datos"]}),(0,d.jsx)(j.BT,{children:"Herramientas avanzadas para administraci\xf3n de datos"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsx)(j.Zp,{className:"p-4",children:(0,d.jsxs)("div",{className:"text-center space-y-2",children:[(0,d.jsx)(A,{className:"h-8 w-8 mx-auto text-muted-foreground"}),(0,d.jsx)("h4",{className:"font-medium",children:"Optimizar DB"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Optimizar rendimiento de la base de datos"}),(0,d.jsx)(f.$,{variant:"outline",size:"sm",children:"Ejecutar"})]})}),(0,d.jsx)(j.Zp,{className:"p-4",children:(0,d.jsxs)("div",{className:"text-center space-y-2",children:[(0,d.jsx)(y,{className:"h-8 w-8 mx-auto text-muted-foreground"}),(0,d.jsx)("h4",{className:"font-medium",children:"Limpiar Cache"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Limpiar cache del sistema"}),(0,d.jsx)(f.$,{variant:"outline",size:"sm",children:"Limpiar"})]})}),(0,d.jsx)(j.Zp,{className:"p-4",children:(0,d.jsxs)("div",{className:"text-center space-y-2",children:[(0,d.jsx)(I.A,{className:"h-8 w-8 mx-auto text-yellow-500"}),(0,d.jsx)("h4",{className:"font-medium",children:"Reiniciar Sistema"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Reiniciar todos los servicios"}),(0,d.jsx)(f.$,{variant:"destructive",size:"sm",children:"Reiniciar"})]})})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"font-medium",children:"Estad\xedsticas del Sistema"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsx)("span",{children:"Uso de CPU"}),(0,d.jsx)("span",{children:"45%"})]}),(0,d.jsx)(o.k,{value:45,className:"h-2"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsx)("span",{children:"Uso de Memoria"}),(0,d.jsx)("span",{children:"62%"})]}),(0,d.jsx)(o.k,{value:62,className:"h-2"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsx)("span",{children:"Espacio en Disco"}),(0,d.jsx)("span",{children:"78%"})]}),(0,d.jsx)(o.k,{value:78,className:"h-2"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsx)("span",{children:"Conexiones Activas"}),(0,d.jsx)("span",{children:"23"})]}),(0,d.jsx)(o.k,{value:23,className:"h-2"})]})]})]})]})]})})]})]})]})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[438,271,17,957,300],()=>b(b.s=91114));module.exports=c})();