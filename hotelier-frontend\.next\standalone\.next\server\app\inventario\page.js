(()=>{var a={};a.id=360,a.ids=[360],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33273:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>x});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(68988),i=c(39390),j=c(59821),k=c(85910),l=c(96752),m=c(37826),n=c(63974),o=c(15616),p=c(88059),q=c(96474),r=c(19080),s=c(43649),t=c(25541),u=c(28561),v=c(99270),w=c(71702);function x(){let[a,b]=(0,e.useState)(""),[c,x]=(0,e.useState)("all"),[y,z]=(0,e.useState)(!0),{toast:A}=(0,w.dj)(),[B,C]=(0,e.useState)([]),[D,E]=(0,e.useState)([]),[F,G]=(0,e.useState)([]),[H,I]=(0,e.useState)({name:"",category:"",minimumStock:"",maximumStock:"",unit:"",unitCost:"",supplier:"",location:""}),[J,K]=(0,e.useState)({type:"",item:"",quantity:"",reason:"",cost:""}),L=a=>{switch(a){case"disponible":return(0,d.jsx)(j.E,{children:"Disponible"});case"bajo_stock":return(0,d.jsx)(j.E,{children:"Bajo Stock"});case"critico":return(0,d.jsx)(j.E,{children:"Cr\xedtico"});case"agotado":return(0,d.jsx)(j.E,{children:"Agotado"});default:return(0,d.jsx)(j.E,{children:a})}},M=B.filter(b=>{let d=b.name.toLowerCase().includes(a.toLowerCase()),e="all"===c||b.category===c;return d&&e}),N=a=>{let b=B.find(b=>b.id===a);if(b){let c=b.maximumStock-b.currentStock;E([{id:`MOV${String(D.length+1).padStart(3,"0")}`,type:"entrada",item:b.name,quantity:c,date:new Date().toISOString().split("T")[0],responsible:"Sistema Autom\xe1tico",reason:"Reposici\xf3n autom\xe1tica",cost:c*b.unitCost},...D]),C(B.map(b=>b.id===a?{...b,currentStock:b.maximumStock,status:"disponible"}:b))}};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Gesti\xf3n de Inventario"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Control de insumos, productos y proveedores"})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(m.lG,{children:[(0,d.jsx)(m.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{variant:"outline",children:[(0,d.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Nuevo Movimiento"]})}),(0,d.jsxs)(m.Cf,{children:[(0,d.jsxs)(m.c7,{children:[(0,d.jsx)(m.L3,{children:"Registrar Movimiento"}),(0,d.jsx)(m.rr,{children:"Registrar entrada o salida de inventario"})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"tipo",children:"Tipo de Movimiento"}),(0,d.jsxs)(n.l6,{value:J.type,onValueChange:a=>K({...J,type:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar tipo"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"entrada",children:"Entrada"}),(0,d.jsx)(n.eb,{value:"salida",children:"Salida"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"item",children:"Item"}),(0,d.jsxs)(n.l6,{value:J.item,onValueChange:a=>K({...J,item:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar item"})}),(0,d.jsx)(n.gC,{children:B.map(a=>(0,d.jsx)(n.eb,{value:a.name,children:a.name},a.id))})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"cantidad",children:"Cantidad"}),(0,d.jsx)(h.p,{id:"cantidad",type:"number",value:J.quantity,onChange:a=>K({...J,quantity:a.target.value}),placeholder:"0"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"costo",children:"Costo Total"}),(0,d.jsx)(h.p,{id:"costo",type:"number",value:J.cost,onChange:a=>K({...J,cost:a.target.value}),placeholder:"0"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"motivo",children:"Motivo"}),(0,d.jsx)(o.T,{id:"motivo",value:J.reason,onChange:a=>K({...J,reason:a.target.value}),placeholder:"Motivo del movimiento..."})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsx)(g.$,{onClick:()=>{J.type&&J.item&&J.quantity&&(E([{id:`MOV${String(D.length+1).padStart(3,"0")}`,type:J.type,item:J.item,quantity:Number.parseInt(J.quantity),date:new Date().toISOString().split("T")[0],responsible:"Usuario Actual",reason:J.reason,cost:Number.parseInt(J.cost)||0},...D]),C(B.map(a=>{if(a.name===J.item){let b="entrada"===J.type?a.currentStock+Number.parseInt(J.quantity):a.currentStock-Number.parseInt(J.quantity),c="disponible";return b<=0?c="agotado":b<=a.minimumStock?c="bajo_stock":b<=1.5*a.minimumStock&&(c="critico"),{...a,currentStock:Math.max(0,b),status:c}}return a})),K({type:"",item:"",quantity:"",reason:"",cost:""}))},children:"Registrar Movimiento"})]})]})]})]}),(0,d.jsxs)(m.lG,{children:[(0,d.jsx)(m.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"Nuevo Item"]})}),(0,d.jsxs)(m.Cf,{className:"max-w-2xl",children:[(0,d.jsxs)(m.c7,{children:[(0,d.jsx)(m.L3,{children:"Nuevo Item de Inventario"}),(0,d.jsx)(m.rr,{children:"Agregar un nuevo producto al inventario"})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"nombre",children:"Nombre del Producto"}),(0,d.jsx)(h.p,{id:"nombre",value:H.name,onChange:a=>I({...H,name:a.target.value}),placeholder:"Nombre del producto"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"category",children:"Categor\xeda"}),(0,d.jsxs)(n.l6,{value:H.category,onValueChange:a=>I({...H,category:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar categor\xeda"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"Lencer\xeda",children:"Lencer\xeda"}),(0,d.jsx)(n.eb,{value:"Amenities",children:"Amenities"}),(0,d.jsx)(n.eb,{value:"Limpieza",children:"Limpieza"}),(0,d.jsx)(n.eb,{value:"Mantenimiento",children:"Mantenimiento"}),(0,d.jsx)(n.eb,{value:"Oficina",children:"Oficina"})]})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"stockMinimo",children:"Stock M\xednimo"}),(0,d.jsx)(h.p,{id:"stockMinimo",type:"number",value:H.minimumStock,onChange:a=>I({...H,minimumStock:a.target.value}),placeholder:"0"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"stockMaximo",children:"Stock M\xe1ximo"}),(0,d.jsx)(h.p,{id:"stockMaximo",type:"number",value:H.maximumStock,onChange:a=>I({...H,maximumStock:a.target.value}),placeholder:"0"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"unidad",children:"Unidad"}),(0,d.jsx)(h.p,{id:"unidad",value:H.unit,onChange:a=>I({...H,unit:a.target.value}),placeholder:"unidades, litros, kg..."})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"costoUnitario",children:"Costo Unitario"}),(0,d.jsx)(h.p,{id:"costoUnitario",type:"number",value:H.unitCost,onChange:a=>I({...H,unitCost:a.target.value}),placeholder:"0"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"supplier",children:"Proveedor"}),(0,d.jsxs)(n.l6,{value:H.supplier,onValueChange:a=>I({...H,supplier:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar proveedor"})}),(0,d.jsx)(n.gC,{children:F.map(a=>(0,d.jsx)(n.eb,{value:a.name,children:a.name},a.id))})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"location",children:"Ubicaci\xf3n en Almac\xe9n"}),(0,d.jsx)(h.p,{id:"location",value:H.location,onChange:a=>I({...H,location:a.target.value}),placeholder:"Ej: Almac\xe9n A-1"})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsx)(g.$,{onClick:()=>{if(H.name&&H.category){let a={id:`INV${String(B.length+1).padStart(3,"0")}`,name:H.name,category:H.category,currentStock:0,minimumStock:Number.parseInt(H.minimumStock)||0,maximumStock:Number.parseInt(H.maximumStock)||0,unit:H.unit,unitCost:Number.parseInt(H.unitCost)||0,supplier:H.supplier,location:H.location,lastPurchaseDate:new Date().toISOString().split("T")[0],status:"agotado"};C([...B,a]),I({name:"",category:"",minimumStock:"",maximumStock:"",unit:"",unitCost:"",supplier:"",location:""})}},children:"Crear Item"})]})]})]})]})]})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Total Items"}),(0,d.jsx)(r.A,{className:"h-4 w-4 text-blue-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:B.length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"En inventario"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Bajo Stock"}),(0,d.jsx)(s.A,{className:"h-4 w-4 text-yellow-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:B.filter(a=>"bajo_stock"===a.status||"critico"===a.status).length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Requieren atenci\xf3n"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Valor Inventario"}),(0,d.jsx)(t.A,{className:"h-4 w-4 text-green-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsxs)("div",{className:"text-2xl font-bold",children:["$",B.reduce((a,b)=>a+b.currentStock*b.unitCost,0).toLocaleString()]}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Valor total"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Movimientos Hoy"}),(0,d.jsx)(u.A,{className:"h-4 w-4 text-purple-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:D.filter(a=>a.date===new Date().toISOString().split("T")[0]).length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Entradas y salidas"})]})]})]}),(0,d.jsxs)(k.tU,{defaultValue:"inventario",className:"space-y-4",children:[(0,d.jsxs)(k.j7,{children:[(0,d.jsx)(k.Xi,{value:"inventario",children:"Inventario"}),(0,d.jsx)(k.Xi,{value:"movimientos",children:"Movimientos"}),(0,d.jsx)(k.Xi,{value:"proveedores",children:"Proveedores"}),(0,d.jsx)(k.Xi,{value:"alertas",children:"Alertas"})]}),(0,d.jsxs)(k.av,{value:"inventario",className:"space-y-4",children:[(0,d.jsxs)(f.Zp,{children:[(0,d.jsx)(f.aR,{children:(0,d.jsx)(f.ZB,{children:"Filtros"})}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(v.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(h.p,{placeholder:"Buscar productos...",value:a,onChange:a=>b(a.target.value),className:"pl-8"})]})}),(0,d.jsxs)(n.l6,{value:c,onValueChange:x,children:[(0,d.jsx)(n.bq,{className:"w-48",children:(0,d.jsx)(n.yv,{placeholder:"Categor\xeda"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"all",children:"Todas las categor\xedas"}),(0,d.jsx)(n.eb,{value:"Lencer\xeda",children:"Lencer\xeda"}),(0,d.jsx)(n.eb,{value:"Amenities",children:"Amenities"}),(0,d.jsx)(n.eb,{value:"Limpieza",children:"Limpieza"}),(0,d.jsx)(n.eb,{value:"Mantenimiento",children:"Mantenimiento"})]})]})]})})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Lista de Inventario"}),(0,d.jsxs)(f.BT,{children:[M.length," productos encontrados"]})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Producto"}),(0,d.jsx)(l.nd,{children:"Categor\xeda"}),(0,d.jsx)(l.nd,{children:"Stock"}),(0,d.jsx)(l.nd,{children:"Min/Max"}),(0,d.jsx)(l.nd,{children:"Costo Unit."}),(0,d.jsx)(l.nd,{children:"Valor Total"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:M.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.location})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(j.E,{variant:"outline",children:a.category})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"font-medium",children:[a.currentStock," ",a.unit]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-1",children:(0,d.jsx)("div",{className:`h-2 rounded-full ${a.currentStock<=a.minimumStock?"bg-red-600":a.currentStock<=1.5*a.minimumStock?"bg-yellow-600":"bg-green-600"}`,style:{width:`${Math.min(a.currentStock/a.maximumStock*100,100)}%`}})})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsxs)("div",{children:["Min: ",a.minimumStock]}),(0,d.jsxs)("div",{children:["Max: ",a.maximumStock]})]})}),(0,d.jsxs)(l.nA,{children:["$",a.unitCost.toLocaleString()]}),(0,d.jsxs)(l.nA,{children:["$",(a.currentStock*a.unitCost).toLocaleString()]}),(0,d.jsx)(l.nA,{children:L(a.status)}),(0,d.jsx)(l.nA,{children:(0,d.jsx)("div",{className:"flex space-x-2",children:("bajo_stock"===a.status||"critico"===a.status||"agotado"===a.status)&&(0,d.jsxs)(g.$,{size:"sm",onClick:()=>N(a.id),children:[(0,d.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Reponer"]})})})]},a.id))})]})})]})]}),(0,d.jsx)(k.av,{value:"movimientos",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Historial de Movimientos"}),(0,d.jsx)(f.BT,{children:"Registro de entradas y salidas de inventario"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Fecha"}),(0,d.jsx)(l.nd,{children:"Tipo"}),(0,d.jsx)(l.nd,{children:"Item"}),(0,d.jsx)(l.nd,{children:"Cantidad"}),(0,d.jsx)(l.nd,{children:"Costo"}),(0,d.jsx)(l.nd,{children:"Responsable"}),(0,d.jsx)(l.nd,{children:"Motivo"})]})}),(0,d.jsx)(l.BF,{children:D.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{children:a.date}),(0,d.jsx)(l.nA,{children:"entrada"===a.type?(0,d.jsx)(j.E,{children:"Entrada"}):(0,d.jsx)(j.E,{children:"Salida"})}),(0,d.jsx)(l.nA,{className:"font-medium",children:a.item}),(0,d.jsx)(l.nA,{children:a.quantity}),(0,d.jsxs)(l.nA,{children:["$",a.cost.toLocaleString()]}),(0,d.jsx)(l.nA,{children:a.responsible}),(0,d.jsx)(l.nA,{children:a.reason})]},a.id))})]})})]})}),(0,d.jsx)(k.av,{value:"proveedores",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Lista de Proveedores"}),(0,d.jsx)(f.BT,{children:"Gesti\xf3n de proveedores y contactos"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Proveedor"}),(0,d.jsx)(l.nd,{children:"Contacto"}),(0,d.jsx)(l.nd,{children:"Categor\xeda"}),(0,d.jsx)(l.nd,{children:"Calificaci\xf3n"}),(0,d.jsx)(l.nd,{children:"Tiempo Entrega"}),(0,d.jsx)(l.nd,{children:"Condiciones"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:F.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.email})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{children:a.contact}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.phone})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(j.E,{variant:"outline",children:a.category})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("span",{className:"text-yellow-500",children:"★"}),(0,d.jsx)("span",{className:"ml-1",children:a.rating})]})}),(0,d.jsx)(l.nA,{children:a.deliveryTime}),(0,d.jsx)(l.nA,{children:a.paymentTerms}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(g.$,{variant:"outline",size:"sm",children:"Contactar"})})]},a.id))})]})})]})}),(0,d.jsx)(k.av,{value:"alertas",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Alertas de Inventario"}),(0,d.jsx)(f.BT,{children:"Items que requieren atenci\xf3n inmediata"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[B.filter(a=>"bajo_stock"===a.status||"critico"===a.status||"agotado"===a.status).map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(s.A,{className:`h-5 w-5 ${"agotado"===a.status?"text-red-600":"critico"===a.status?"text-red-500":"text-yellow-500"}`}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Stock actual: ",a.currentStock," ",a.unit," ","(M\xednimo: ",a.minimumStock,")"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[L(a.status),(0,d.jsxs)(g.$,{size:"sm",onClick:()=>N(a.id),children:[(0,d.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Reponer"]})]})]},a.id)),0===B.filter(a=>"bajo_stock"===a.status||"critico"===a.status||"agotado"===a.status).length&&(0,d.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,d.jsx)(r.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,d.jsx)("p",{children:"No hay alertas de inventario en este momento"})]})]})})]})})]})]})}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43649:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44667:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\inventario\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\inventario\\page.tsx","default")},46876:(a,b,c)=>{Promise.resolve().then(c.bind(c,44667))},56604:(a,b,c)=>{Promise.resolve().then(c.bind(c,33273))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88324:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["inventario",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,44667)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\inventario\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(c.bind(c,98336)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\inventario\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\inventario\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/inventario/page",pathname:"/inventario",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/inventario/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},96487:()=>{},98336:(a,b,c)=>{"use strict";function d(){return null}c.r(b),c.d(b,{default:()=>d})}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[438,271,957,381],()=>b(b.s=88324));module.exports=c})();