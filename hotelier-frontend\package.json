{"name": "hotelier-frontend", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-label": "latest", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "latest", "lucide-react": "^0.536.0", "next": "15.4.5", "next-themes": "latest", "react": "^19", "react-day-picker": "9.8.1", "react-dom": "^19", "recharts": "3.1.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.32.0", "eslint-config-next": "15.4.5", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}