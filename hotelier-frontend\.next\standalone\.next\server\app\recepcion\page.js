(()=>{var a={};a.id=91,a.ids=[91],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14804:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\recepcion\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\recepcion\\page.tsx","default")},15616:(a,b,c)=>{"use strict";c.d(b,{T:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:c,...b}));g.displayName="Textarea"},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},36318:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>w});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(68988),i=c(39390),j=c(59821),k=c(85910),l=c(96752),m=c(37826),n=c(15616),o=c(93508);let p=(0,c(62688).A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var q=c(97051),r=c(41312),s=c(19959),t=c(48730),u=c(58887);c(59556);var v=c(71702);function w(){let[a,b]=(0,e.useState)(null),[c,w]=(0,e.useState)(!0),{toast:x}=(0,v.dj)(),[y,z]=(0,e.useState)([]),[A,B]=(0,e.useState)([]),[C,D]=(0,e.useState)([]),[E,F]=(0,e.useState)([]),G=a=>{switch(a){case"pendiente":return(0,d.jsx)(j.E,{className:"bg-yellow-100 text-yellow-800",children:"Pendiente"});case"completado":return(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Completado"});case"en_proceso":return(0,d.jsx)(j.E,{className:"bg-blue-100 text-blue-800",children:"En Proceso"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Recepci\xf3n y Front Desk"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Gesti\xf3n de hu\xe9spedes y servicios de recepci\xf3n"})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Check-ins Hoy"}),(0,d.jsx)(o.A,{className:"h-4 w-4 text-green-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"8"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"2 completados"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Check-outs Hoy"}),(0,d.jsx)(p,{className:"h-4 w-4 text-red-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"5"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"3 pendientes"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Solicitudes"}),(0,d.jsx)(q.A,{className:"h-4 w-4 text-orange-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"12"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"3 pendientes"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Hu\xe9spedes Actuales"}),(0,d.jsx)(r.A,{className:"h-4 w-4 text-blue-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"156"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"En 102 habitaciones"})]})]})]}),(0,d.jsxs)(k.tU,{defaultValue:"checkin",className:"space-y-4",children:[(0,d.jsxs)(k.j7,{children:[(0,d.jsx)(k.Xi,{value:"checkin",children:"Check-in"}),(0,d.jsx)(k.Xi,{value:"checkout",children:"Check-out"}),(0,d.jsx)(k.Xi,{value:"solicitudes",children:"Solicitudes"}),(0,d.jsx)(k.Xi,{value:"huespedes",children:"Hu\xe9spedes Actuales"})]}),(0,d.jsx)(k.av,{value:"checkin",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Check-ins de Hoy"}),(0,d.jsx)(f.BT,{children:"Hu\xe9spedes programados para ingresar hoy"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Hu\xe9sped"}),(0,d.jsx)(l.nd,{children:"Habitaci\xf3n"}),(0,d.jsx)(l.nd,{children:"Hora Llegada"}),(0,d.jsx)(l.nd,{children:"Hu\xe9spedes"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:y.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.guest}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.phone})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.room}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.roomType})]})}),(0,d.jsx)(l.nA,{children:a.arrivalTime}),(0,d.jsx)(l.nA,{children:a.guests}),(0,d.jsx)(l.nA,{children:G(a.status)}),(0,d.jsx)(l.nA,{children:(0,d.jsx)("div",{className:"flex space-x-2",children:"pendiente"===a.status?(0,d.jsxs)(m.lG,{children:[(0,d.jsx)(m.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{size:"sm",children:[(0,d.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Check-in"]})}),(0,d.jsxs)(m.Cf,{children:[(0,d.jsxs)(m.c7,{children:[(0,d.jsx)(m.L3,{children:"Realizar Check-in"}),(0,d.jsxs)(m.rr,{children:["Completar el proceso de check-in para"," ",a.guest]})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{children:"Hu\xe9sped"}),(0,d.jsx)(h.p,{value:a.guest,disabled:!0})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{children:"Habitaci\xf3n"}),(0,d.jsx)(h.p,{value:a.room,disabled:!0})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"document",children:"Documento de Identidad"}),(0,d.jsx)(h.p,{id:"document",placeholder:"N\xfamero de documento"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"checkInNotes",children:"Observaciones"}),(0,d.jsx)(n.T,{id:"checkInNotes",placeholder:"Observaciones del check-in..."})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsxs)(g.$,{onClick:()=>{var b;return b=a.id,void z(y.map(a=>a.id===b?{...a,status:"completado"}:a))},children:[(0,d.jsx)(s.A,{className:"mr-2 h-4 w-4"}),"Entregar Llaves"]})]})]})]})]}):(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Completado"})})})]},a.id))})]})})]})}),(0,d.jsx)(k.av,{value:"checkout",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Check-outs de Hoy"}),(0,d.jsx)(f.BT,{children:"Hu\xe9spedes programados para salir hoy"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Hu\xe9sped"}),(0,d.jsx)(l.nd,{children:"Habitaci\xf3n"}),(0,d.jsx)(l.nd,{children:"Hora Salida"}),(0,d.jsx)(l.nd,{children:"Consumos"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:A.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.guest}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.phone})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.room}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.roomType})]})}),(0,d.jsx)(l.nA,{children:a.departureTime}),(0,d.jsxs)(l.nA,{children:["$",a.consumption?.toLocaleString()]}),(0,d.jsx)(l.nA,{children:G(a.status)}),(0,d.jsx)(l.nA,{children:(0,d.jsx)("div",{className:"flex space-x-2",children:"pendiente"===a.status?(0,d.jsxs)(m.lG,{children:[(0,d.jsx)(m.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{size:"sm",variant:"outline",children:[(0,d.jsx)(p,{className:"mr-2 h-4 w-4"}),"Check-out"]})}),(0,d.jsxs)(m.Cf,{children:[(0,d.jsxs)(m.c7,{children:[(0,d.jsx)(m.L3,{children:"Realizar Check-out"}),(0,d.jsxs)(m.rr,{children:["Completar el proceso de check-out para"," ",a.guest]})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{children:"Hu\xe9sped"}),(0,d.jsx)(h.p,{value:a.guest,disabled:!0})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{children:"Habitaci\xf3n"}),(0,d.jsx)(h.p,{value:a.room,disabled:!0})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{children:"Consumos Adicionales"}),(0,d.jsx)(h.p,{value:`$${a.consumption?.toLocaleString()}`,disabled:!0})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"checkOutNotes",children:"Observaciones"}),(0,d.jsx)(n.T,{id:"checkOutNotes",placeholder:"Observaciones del check-out..."})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsx)(g.$,{onClick:()=>{var b;return b=a.id,void B(A.map(a=>a.id===b?{...a,status:"completado"}:a))},children:"Completar Check-out"})]})]})]})]}):(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Completado"})})})]},a.id))})]})})]})}),(0,d.jsx)(k.av,{value:"solicitudes",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Solicitudes de Hu\xe9spedes"}),(0,d.jsx)(f.BT,{children:"Gesti\xf3n de solicitudes y servicios"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Habitaci\xf3n"}),(0,d.jsx)(l.nd,{children:"Hu\xe9sped"}),(0,d.jsx)(l.nd,{children:"Tipo"}),(0,d.jsx)(l.nd,{children:"Descripci\xf3n"}),(0,d.jsx)(l.nd,{children:"Hora"}),(0,d.jsx)(l.nd,{children:"Prioridad"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:C.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{className:"font-medium",children:a.room}),(0,d.jsx)(l.nA,{children:a.guest}),(0,d.jsx)(l.nA,{children:a.type}),(0,d.jsx)(l.nA,{children:a.description}),(0,d.jsx)(l.nA,{children:a.time}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"alta":return(0,d.jsx)(j.E,{variant:"destructive",children:"Alta"});case"media":return(0,d.jsx)(j.E,{className:"bg-yellow-100 text-yellow-800",children:"Media"});case"baja":return(0,d.jsx)(j.E,{variant:"secondary",children:"Baja"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(a.priority)}),(0,d.jsx)(l.nA,{children:G(a.status)}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"flex space-x-2",children:["pendiente"===a.status&&(0,d.jsxs)(g.$,{size:"sm",variant:"outline",onClick:()=>{var b;return b=a.id,void D(C.map(a=>a.id===b?{...a,status:"en_proceso"}:a))},children:[(0,d.jsx)(t.A,{className:"mr-2 h-4 w-4"}),"Procesar"]}),(0,d.jsx)(g.$,{size:"sm",variant:"ghost",children:(0,d.jsx)(u.A,{className:"h-4 w-4"})})]})})]},a.id))})]})})]})}),(0,d.jsx)(k.av,{value:"huespedes",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Hu\xe9spedes Actuales"}),(0,d.jsx)(f.BT,{children:"Hu\xe9spedes actualmente en el hotel"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Habitaci\xf3n"}),(0,d.jsx)(l.nd,{children:"Hu\xe9sped"}),(0,d.jsx)(l.nd,{children:"Tipo"}),(0,d.jsx)(l.nd,{children:"Entrada"}),(0,d.jsx)(l.nd,{children:"Salida"}),(0,d.jsx)(l.nd,{children:"Hu\xe9spedes"}),(0,d.jsx)(l.nd,{children:"Estado"})]})}),(0,d.jsx)(l.BF,{children:E.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{className:"font-medium",children:a.room}),(0,d.jsx)(l.nA,{children:a.guest}),(0,d.jsx)(l.nA,{children:a.roomType}),(0,d.jsx)(l.nA,{children:a.checkInDate}),(0,d.jsx)(l.nA,{children:a.checkOutDate}),(0,d.jsx)(l.nA,{children:a.guests}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Ocupada"})})]},a.room))})]})})]})})]})]})}},37826:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>m,L3:()=>o,c7:()=>n,lG:()=>i,rr:()=>p,zM:()=>j});var d=c(60687),e=c(43210),f=c(26134),g=c(11860),h=c(96241);let i=f.bL,j=f.l9,k=f.ZL;f.bm;let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hJ,{ref:c,className:(0,h.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...b}));l.displayName=f.hJ.displayName;let m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(k,{children:[(0,d.jsx)(l,{}),(0,d.jsxs)(f.UC,{ref:e,className:(0,h.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[b,(0,d.jsxs)(f.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=f.UC.displayName;let n=({className:a,...b})=>(0,d.jsx)("div",{className:(0,h.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...b});n.displayName="DialogHeader";let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hE,{ref:c,className:(0,h.cn)("text-lg font-semibold leading-none tracking-tight",a),...b}));o.displayName=f.hE.displayName;let p=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.VY,{ref:c,className:(0,h.cn)("text-sm text-muted-foreground",a),...b}));p.displayName=f.VY.displayName},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},45751:(a,b,c)=>{Promise.resolve().then(c.bind(c,14804))},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},55146:(a,b,c)=>{"use strict";c.d(b,{B8:()=>D,UC:()=>F,bL:()=>C,l9:()=>E});var d=c(43210),e=c(70569),f=c(11273),g=c(72942),h=c(46059),i=c(14163),j=c(43),k=c(65551),l=c(96963),m=c(60687),n="Tabs",[o,p]=(0,f.A)(n,[g.RG]),q=(0,g.RG)(),[r,s]=o(n),t=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,onValueChange:e,defaultValue:f,orientation:g="horizontal",dir:h,activationMode:o="automatic",...p}=a,q=(0,j.jH)(h),[s,t]=(0,k.i)({prop:d,onChange:e,defaultProp:f??"",caller:n});return(0,m.jsx)(r,{scope:c,baseId:(0,l.B)(),value:s,onValueChange:t,orientation:g,dir:q,activationMode:o,children:(0,m.jsx)(i.sG.div,{dir:q,"data-orientation":g,...p,ref:b})})});t.displayName=n;var u="TabsList",v=d.forwardRef((a,b)=>{let{__scopeTabs:c,loop:d=!0,...e}=a,f=s(u,c),h=q(c);return(0,m.jsx)(g.bL,{asChild:!0,...h,orientation:f.orientation,dir:f.dir,loop:d,children:(0,m.jsx)(i.sG.div,{role:"tablist","aria-orientation":f.orientation,...e,ref:b})})});v.displayName=u;var w="TabsTrigger",x=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,disabled:f=!1,...h}=a,j=s(w,c),k=q(c),l=A(j.baseId,d),n=B(j.baseId,d),o=d===j.value;return(0,m.jsx)(g.q7,{asChild:!0,...k,focusable:!f,active:o,children:(0,m.jsx)(i.sG.button,{type:"button",role:"tab","aria-selected":o,"aria-controls":n,"data-state":o?"active":"inactive","data-disabled":f?"":void 0,disabled:f,id:l,...h,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f||0!==a.button||!1!==a.ctrlKey?a.preventDefault():j.onValueChange(d)}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&j.onValueChange(d)}),onFocus:(0,e.m)(a.onFocus,()=>{let a="manual"!==j.activationMode;o||f||!a||j.onValueChange(d)})})})});x.displayName=w;var y="TabsContent",z=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,forceMount:f,children:g,...j}=a,k=s(y,c),l=A(k.baseId,e),n=B(k.baseId,e),o=e===k.value,p=d.useRef(o);return d.useEffect(()=>{let a=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,m.jsx)(h.C,{present:f||o,children:({present:c})=>(0,m.jsx)(i.sG.div,{"data-state":o?"active":"inactive","data-orientation":k.orientation,role:"tabpanel","aria-labelledby":l,hidden:!c,id:n,tabIndex:0,...j,ref:b,style:{...a.style,animationDuration:p.current?"0s":void 0},children:c&&g})})});function A(a,b){return`${a}-trigger-${b}`}function B(a,b){return`${a}-content-${b}`}z.displayName=y;var C=t,D=v,E=x,F=z},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},59821:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(96241);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74068:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["recepcion",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,14804)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\recepcion\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\recepcion\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/recepcion/page",pathname:"/recepcion",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/recepcion/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},78148:(a,b,c)=>{"use strict";c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},85910:(a,b,c)=>{"use strict";c.d(b,{Xi:()=>j,av:()=>k,j7:()=>i,tU:()=>h});var d=c(60687),e=c(43210),f=c(55146),g=c(96241);let h=f.bL,i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.B8,{ref:c,className:(0,g.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...b}));i.displayName=f.B8.displayName;let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.l9,{ref:c,className:(0,g.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...b}));j.displayName=f.l9.displayName;let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.UC,{ref:c,className:(0,g.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...b}));k.displayName=f.UC.displayName},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96752:(a,b,c)=>{"use strict";c.d(b,{A0:()=>h,BF:()=>i,Hj:()=>j,XI:()=>g,nA:()=>l,nd:()=>k});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{className:"relative w-full overflow-auto",children:(0,d.jsx)("table",{ref:c,className:(0,f.cn)("w-full caption-bottom text-sm",a),...b})}));g.displayName="Table";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("thead",{ref:c,className:(0,f.cn)("[&_tr]:border-b",a),...b}));h.displayName="TableHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tbody",{ref:c,className:(0,f.cn)("[&_tr:last-child]:border-0",a),...b}));i.displayName="TableBody",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tfoot",{ref:c,className:(0,f.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...b})).displayName="TableFooter";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tr",{ref:c,className:(0,f.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...b}));j.displayName="TableRow";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("th",{ref:c,className:(0,f.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...b}));k.displayName="TableHead";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("td",{ref:c,className:(0,f.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...b}));l.displayName="TableCell",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("caption",{ref:c,className:(0,f.cn)("mt-4 text-sm text-muted-foreground",a),...b})).displayName="TableCaption"},98895:(a,b,c)=>{Promise.resolve().then(c.bind(c,36318))}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[438,957],()=>b(b.s=74068));module.exports=c})();