(()=>{var a={};a.id=862,a.ids=[862],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18731:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(59821),i=c(85910),j=c(96752),k=c(37826),l=c(63974),m=c(15616),n=c(39390),o=c(82679),p=c(5336),q=c(48730),r=c(41312),s=c(5263);c(59556);var t=c(71702);function u(){let[a,b]=(0,e.useState)(null),[c,u]=(0,e.useState)(!0),{toast:v}=(0,t.dj)(),[w,x]=(0,e.useState)([]),[y,z]=(0,e.useState)([]),[A,B]=(0,e.useState)([]);return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Gesti\xf3n de Limpieza y Mantenimiento"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Control del estado de rooms y mantenimiento"})]}),(0,d.jsxs)(k.lG,{children:[(0,d.jsx)(k.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{children:[(0,d.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Reportar Incidencia"]})}),(0,d.jsxs)(k.Cf,{children:[(0,d.jsxs)(k.c7,{children:[(0,d.jsx)(k.L3,{children:"Reportar Incidencia"}),(0,d.jsx)(k.rr,{children:"Crear un nuevo report de mantenimiento"})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"room",children:"Habitaci\xf3n"}),(0,d.jsxs)(l.l6,{children:[(0,d.jsx)(l.bq,{children:(0,d.jsx)(l.yv,{placeholder:"Seleccionar habitaci\xf3n"})}),(0,d.jsx)(l.gC,{children:w.map(a=>(0,d.jsxs)(l.eb,{value:a.number,children:[a.number," - ",a.type]},a.number))})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"tipo",children:"Tipo de Incidencia"}),(0,d.jsxs)(l.l6,{children:[(0,d.jsx)(l.bq,{children:(0,d.jsx)(l.yv,{placeholder:"Seleccionar tipo"})}),(0,d.jsxs)(l.gC,{children:[(0,d.jsx)(l.eb,{value:"plomeria",children:"Plomer\xeda"}),(0,d.jsx)(l.eb,{value:"electrico",children:"El\xe9ctrico"}),(0,d.jsx)(l.eb,{value:"aire",children:"Aire Acondicionado"}),(0,d.jsx)(l.eb,{value:"mobiliario",children:"Mobiliario"}),(0,d.jsx)(l.eb,{value:"otros",children:"Otros"})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"prioridad",children:"Prioridad"}),(0,d.jsxs)(l.l6,{children:[(0,d.jsx)(l.bq,{children:(0,d.jsx)(l.yv,{placeholder:"Seleccionar prioridad"})}),(0,d.jsxs)(l.gC,{children:[(0,d.jsx)(l.eb,{value:"alta",children:"Alta"}),(0,d.jsx)(l.eb,{value:"media",children:"Media"}),(0,d.jsx)(l.eb,{value:"baja",children:"Baja"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"description",children:"Descripci\xf3n"}),(0,d.jsx)(m.T,{id:"description",placeholder:"Describe la incidencia..."})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsx)(g.$,{children:"Crear Reporte"})]})]})]})]})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Habitaciones Limpias"}),(0,d.jsx)(p.A,{className:"h-4 w-4 text-green-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"85"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"De 120 rooms"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Pendientes Limpieza"}),(0,d.jsx)(q.A,{className:"h-4 w-4 text-orange-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"15"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Por limpiar"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"En Mantenimiento"}),(0,d.jsx)(o.A,{className:"h-4 w-4 text-red-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"8"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Fuera de servicio"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Personal Activo"}),(0,d.jsx)(r.A,{className:"h-4 w-4 text-blue-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"12"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"De 15 employees"})]})]})]}),(0,d.jsxs)(i.tU,{defaultValue:"rooms",className:"space-y-4",children:[(0,d.jsxs)(i.j7,{children:[(0,d.jsx)(i.Xi,{value:"rooms",children:"Estado Habitaciones"}),(0,d.jsx)(i.Xi,{value:"mantenimiento",children:"Mantenimiento"}),(0,d.jsx)(i.Xi,{value:"personal",children:"Personal"})]}),(0,d.jsx)(i.av,{value:"rooms",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Estado de Habitaciones"}),(0,d.jsx)(f.BT,{children:"Control en tiempo real del estado de limpieza"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(j.XI,{children:[(0,d.jsx)(j.A0,{children:(0,d.jsxs)(j.Hj,{children:[(0,d.jsx)(j.nd,{children:"Habitaci\xf3n"}),(0,d.jsx)(j.nd,{children:"Tipo"}),(0,d.jsx)(j.nd,{children:"Estado"}),(0,d.jsx)(j.nd,{children:"\xdaltima Limpieza"}),(0,d.jsx)(j.nd,{children:"Pr\xf3ximo Hu\xe9sped"}),(0,d.jsx)(j.nd,{children:"Empleado"}),(0,d.jsx)(j.nd,{children:"Acciones"})]})}),(0,d.jsx)(j.BF,{children:w.map(a=>(0,d.jsxs)(j.Hj,{children:[(0,d.jsx)(j.nA,{className:"font-medium",children:a.number}),(0,d.jsx)(j.nA,{children:a.type}),(0,d.jsx)(j.nA,{children:(a=>{switch(a){case"limpia":return(0,d.jsx)(h.E,{className:"bg-green-100 text-green-800",children:"Limpia"});case"sucia":return(0,d.jsx)(h.E,{className:"bg-red-100 text-red-800",children:"Sucia"});case"en_limpieza":return(0,d.jsx)(h.E,{className:"bg-blue-100 text-blue-800",children:"En Limpieza"});case"mantenimiento":return(0,d.jsx)(h.E,{className:"bg-orange-100 text-orange-800",children:"Mantenimiento"});case"ocupada":return(0,d.jsx)(h.E,{className:"bg-purple-100 text-purple-800",children:"Ocupada"});default:return(0,d.jsx)(h.E,{variant:"secondary",children:a})}})(a.status)}),(0,d.jsx)(j.nA,{children:a.lastCleaning}),(0,d.jsx)(j.nA,{children:a.nextGuest}),(0,d.jsx)(j.nA,{children:a.assignedEmployee}),(0,d.jsx)(j.nA,{children:(0,d.jsxs)("div",{className:"flex space-x-2",children:["sucia"===a.status&&(0,d.jsxs)(g.$,{size:"sm",onClick:()=>{var b;return b=a.number,void x(w.map(a=>a.number===b?{...a,status:"en_limpieza"}:a))},children:[(0,d.jsx)(s.A,{className:"mr-2 h-4 w-4"}),"Iniciar Limpieza"]}),"en_limpieza"===a.status&&(0,d.jsxs)(g.$,{size:"sm",variant:"outline",onClick:()=>{var b;return b=a.number,void x(w.map(a=>a.number===b?{...a,status:"limpia",lastCleaning:new Date().toISOString().slice(0,16).replace("T"," ")}:a))},children:[(0,d.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Completar"]}),"limpia"===a.status&&(0,d.jsx)(h.E,{className:"bg-green-100 text-green-800",children:"Lista"})]})})]},a.number))})]})})]})}),(0,d.jsx)(i.av,{value:"mantenimiento",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Reportes de Mantenimiento"}),(0,d.jsx)(f.BT,{children:"Gesti\xf3n de incidencias y reparaciones"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(j.XI,{children:[(0,d.jsx)(j.A0,{children:(0,d.jsxs)(j.Hj,{children:[(0,d.jsx)(j.nd,{children:"ID"}),(0,d.jsx)(j.nd,{children:"Habitaci\xf3n"}),(0,d.jsx)(j.nd,{children:"Tipo"}),(0,d.jsx)(j.nd,{children:"Descripci\xf3n"}),(0,d.jsx)(j.nd,{children:"Prioridad"}),(0,d.jsx)(j.nd,{children:"Estado"}),(0,d.jsx)(j.nd,{children:"T\xe9cnico"}),(0,d.jsx)(j.nd,{children:"Tiempo Est."}),(0,d.jsx)(j.nd,{children:"Acciones"})]})}),(0,d.jsx)(j.BF,{children:A.map(a=>(0,d.jsxs)(j.Hj,{children:[(0,d.jsx)(j.nA,{className:"font-medium",children:a.id}),(0,d.jsx)(j.nA,{children:a.room}),(0,d.jsx)(j.nA,{children:a.type}),(0,d.jsx)(j.nA,{children:a.description}),(0,d.jsx)(j.nA,{children:(a=>{switch(a){case"alta":return(0,d.jsx)(h.E,{variant:"destructive",children:"Alta"});case"media":return(0,d.jsx)(h.E,{className:"bg-yellow-100 text-yellow-800",children:"Media"});case"baja":return(0,d.jsx)(h.E,{variant:"secondary",children:"Baja"});default:return(0,d.jsx)(h.E,{variant:"secondary",children:a})}})(a.priority)}),(0,d.jsx)(j.nA,{children:(a=>{switch(a){case"pendiente":return(0,d.jsx)(h.E,{className:"bg-yellow-100 text-yellow-800",children:"Pendiente"});case"en_proceso":return(0,d.jsx)(h.E,{className:"bg-blue-100 text-blue-800",children:"En Proceso"});case"completado":return(0,d.jsx)(h.E,{className:"bg-green-100 text-green-800",children:"Completado"});default:return(0,d.jsx)(h.E,{variant:"secondary",children:a})}})(a.status)}),(0,d.jsx)(j.nA,{children:a.assignedTechnician}),(0,d.jsx)(j.nA,{children:a.estimatedTime}),(0,d.jsx)(j.nA,{children:(0,d.jsxs)("div",{className:"flex space-x-2",children:["pendiente"===a.status&&(0,d.jsxs)(g.$,{size:"sm",variant:"outline",onClick:()=>{var b;return b=a.id,void B(A.map(a=>a.id===b?{...a,status:"en_proceso"}:a))},children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"Iniciar"]}),"en_proceso"===a.status&&(0,d.jsxs)(g.$,{size:"sm",onClick:()=>{var b;return b=a.id,void B(A.map(a=>a.id===b?{...a,status:"completado"}:a))},children:[(0,d.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Completar"]})]})})]},a.id))})]})})]})}),(0,d.jsx)(i.av,{value:"personal",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Personal de Limpieza"}),(0,d.jsx)(f.BT,{children:"Estado y asignaciones del personal"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(j.XI,{children:[(0,d.jsx)(j.A0,{children:(0,d.jsxs)(j.Hj,{children:[(0,d.jsx)(j.nd,{children:"Empleado"}),(0,d.jsx)(j.nd,{children:"Turno"}),(0,d.jsx)(j.nd,{children:"Asignadas"}),(0,d.jsx)(j.nd,{children:"Completadas"}),(0,d.jsx)(j.nd,{children:"Progreso"}),(0,d.jsx)(j.nd,{children:"Estado"}),(0,d.jsx)(j.nd,{children:"Ubicaci\xf3n"})]})}),(0,d.jsx)(j.BF,{children:y.map(a=>(0,d.jsxs)(j.Hj,{children:[(0,d.jsx)(j.nA,{className:"font-medium",children:a.name}),(0,d.jsx)(j.nA,{children:a.shift}),(0,d.jsx)(j.nA,{children:a.assignedRooms}),(0,d.jsx)(j.nA,{children:a.completedRooms}),(0,d.jsx)(j.nA,{children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${a.completedRooms/a.assignedRooms*100}%`}})}),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:[Math.round(a.completedRooms/a.assignedRooms*100),"%"]})]})}),(0,d.jsx)(j.nA,{children:(0,d.jsx)(h.E,{className:"activo"===a.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800",children:"activo"===a.status?"Activo":"Descanso"})}),(0,d.jsx)(j.nA,{children:a.currentLocation})]},a.id))})]})})]})})]})]})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27214:(a,b,c)=>{Promise.resolve().then(c.bind(c,18731))},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66534:(a,b,c)=>{Promise.resolve().then(c.bind(c,86157))},72544:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["limpieza",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,86157)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\limpieza\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\limpieza\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/limpieza/page",pathname:"/limpieza",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/limpieza/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},86157:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\limpieza\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\limpieza\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[438,271,957,867],()=>b(b.s=72544));module.exports=c})();