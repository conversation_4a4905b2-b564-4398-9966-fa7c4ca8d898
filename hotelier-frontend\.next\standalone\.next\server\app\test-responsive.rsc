1:"$Sreact.fragment"
2:I[94970,[],"ClientSegmentRoot"]
3:I[70909,["1778","static/chunks/1778-ff7f41a4327b2f02.js","1915","static/chunks/1915-f4ae9c03b20697e2.js","6961","static/chunks/6961-e978919a41d767a5.js","8592","static/chunks/8592-f00099624b2cafcb.js","6302","static/chunks/6302-479287e0c72c4a46.js","1496","static/chunks/1496-63849f4f47cbfc4c.js","7177","static/chunks/app/layout-45c5bd505fc34d81.js"],"default"]
4:I[87555,[],""]
5:I[31295,[],""]
e:I[28393,[],""]
:HL["/_next/static/css/fc37c8c810b334bd.css","style"]
0:{"P":null,"b":"8WeMD5X14Yx8gyQfnpumx","p":"","c":["","test-responsive"],"i":false,"f":[[["",{"children":["test-responsive",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/fc37c8c810b334bd.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","$L2",null,{"Component":"$3","slots":{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]},"params":{},"promise":"$@6"}]]}],{"children":["test-responsive",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[[["$","nav",null,{"ref":"$undefined","aria-label":"breadcrumb","children":["$","ol",null,{"ref":"$undefined","className":"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5","children":[["$","li",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5","children":["$","a",null,{"ref":"$undefined","className":"transition-colors hover:text-foreground","href":"/","children":"Dashboard"}]}],["$","li",null,{"role":"presentation","aria-hidden":"true","className":"[&>svg]:w-3.5 [&>svg]:h-3.5","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-chevron-right","aria-hidden":"true","children":[["$","path","mthhwq",{"d":"m9 18 6-6-6-6"}],"$undefined"]}]}],["$","li",null,{"ref":"$undefined","className":"inline-flex items-center gap-1.5","children":["$","span",null,{"ref":"$undefined","role":"link","aria-disabled":"true","aria-current":"page","className":"font-normal text-foreground","children":"Test Responsivo"}]}]]}]}],["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold","children":"Test de Responsividad - Hotelier"}],["$","p",null,{"className":"text-muted-foreground","children":"Prueba el sidebar collapsible en diferentes tamaños de pantalla"}]]}],["$","div",null,{"className":"grid gap-6 md:grid-cols-2 lg:grid-cols-3","children":[["$","div",null,{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-monitor h-5 w-5","aria-hidden":"true","children":[["$","rect","48i651",{"width":"20","height":"14","x":"2","y":"3","rx":"2"}],["$","line","1svkeh",{"x1":"8","x2":"16","y1":"21","y2":"21"}],["$","line","vw1qmm",{"x1":"12","x2":"12","y1":"17","y2":"21"}],"$undefined"]}],"Desktop"]}],["$","div",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"Pantallas grandes (≥ 1024px)"}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80","children":"Sidebar expandido por defecto"}],"$L7"]}]}]]}],"$L8","$L9"]}],"$La","$Lb"]}]],null,"$Lc"]}],{},null,false]},null,false]},null,false],"$Ld",false]],"m":"$undefined","G":["$e",[]],"s":false,"S":true}
f:I[59665,[],"OutletBoundary"]
11:I[74911,[],"AsyncMetadataOutlet"]
13:I[59665,[],"ViewportBoundary"]
15:I[59665,[],"MetadataBoundary"]
16:"$Sreact.suspense"
7:["$","p",null,{"className":"text-sm text-muted-foreground","children":"El sidebar se muestra completamente expandido con todas las etiquetas visibles."}]
8:["$","div",null,{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tablet h-5 w-5","aria-hidden":"true","children":[["$","rect","76otgf",{"width":"16","height":"20","x":"4","y":"2","rx":"2","ry":"2"}],["$","line","1dp563",{"x1":"12","x2":"12.01","y1":"18","y2":"18"}],"$undefined"]}],"Tablet"]}],["$","div",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"Pantallas medianas (768px - 1023px)"}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80","children":"Sidebar collapsible"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"El sidebar puede colapsarse para dar más espacio al contenido principal."}]]}]}]]}]
9:["$","div",null,{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-smartphone h-5 w-5","aria-hidden":"true","children":[["$","rect","1yt0o3",{"width":"14","height":"20","x":"5","y":"2","rx":"2","ry":"2"}],["$","path","mhygvu",{"d":"M12 18h.01"}],"$undefined"]}],"Móvil"]}],["$","div",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"Pantallas pequeñas (< 768px)"}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80","children":"Sidebar como overlay"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"El sidebar se muestra como un overlay que se puede abrir/cerrar con el botón de menú."}]]}]}]]}]
a:["$","div",null,{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","div",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight","children":"Funcionalidades del Sidebar"}],["$","div",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"Características principales del sidebar responsivo"}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","div",null,{"className":"grid gap-4 md:grid-cols-2","children":[["$","div",null,{"className":"space-y-3","children":[["$","h4",null,{"className":"font-medium","children":"Funcionalidades Principales:"}],["$","ul",null,{"className":"space-y-2 text-sm text-muted-foreground","children":[["$","li",null,{"className":"flex items-center gap-2","children":[["$","div",null,{"className":"h-1 w-1 bg-primary rounded-full"}],"Collapsible manual con botón toggle"]}],["$","li",null,{"className":"flex items-center gap-2","children":[["$","div",null,{"className":"h-1 w-1 bg-primary rounded-full"}],"Adaptación automática según el tamaño de pantalla"]}],["$","li",null,{"className":"flex items-center gap-2","children":[["$","div",null,{"className":"h-1 w-1 bg-primary rounded-full"}],"Navegación agrupada por categorías"]}],["$","li",null,{"className":"flex items-center gap-2","children":[["$","div",null,{"className":"h-1 w-1 bg-primary rounded-full"}],"Indicador visual de página activa"]}]]}]]}],["$","div",null,{"className":"space-y-3","children":[["$","h4",null,{"className":"font-medium","children":"Mejoras Responsivas:"}],["$","ul",null,{"className":"space-y-2 text-sm text-muted-foreground","children":[["$","li",null,{"className":"flex items-center gap-2","children":[["$","div",null,{"className":"h-1 w-1 bg-primary rounded-full"}],"Overlay en dispositivos móviles"]}],["$","li",null,{"className":"flex items-center gap-2","children":[["$","div",null,{"className":"h-1 w-1 bg-primary rounded-full"}],"Transiciones suaves entre estados"]}],["$","li",null,{"className":"flex items-center gap-2","children":[["$","div",null,{"className":"h-1 w-1 bg-primary rounded-full"}],"Breadcrumbs para mejor navegación"]}],["$","li",null,{"className":"flex items-center gap-2","children":[["$","div",null,{"className":"h-1 w-1 bg-primary rounded-full"}],"Iconos descriptivos para cada sección"]}]]}]]}]]}]}]]}]
b:["$","div",null,{"ref":"$undefined","className":"rounded-lg border bg-card text-card-foreground shadow-sm","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":["$","div",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight","children":"Instrucciones de Uso"}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","div",null,{"className":"space-y-4","children":[["$","div",null,{"className":"flex items-start gap-3","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-menu h-5 w-5 mt-0.5 text-primary","aria-hidden":"true","children":[["$","path","1lakjw",{"d":"M4 12h16"}],["$","path","19g7jn",{"d":"M4 18h16"}],["$","path","1o0s65",{"d":"M4 6h16"}],"$undefined"]}],["$","div",null,{"children":[["$","p",null,{"className":"font-medium","children":"Botón de Toggle"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Haz clic en el icono de menú (☰) en la parte superior izquierda para colapsar/expandir el sidebar."}]]}]]}],["$","div",null,{"className":"flex items-start gap-3","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-monitor h-5 w-5 mt-0.5 text-primary","aria-hidden":"true","children":[["$","rect","48i651",{"width":"20","height":"14","x":"2","y":"3","rx":"2"}],["$","line","1svkeh",{"x1":"8","x2":"16","y1":"21","y2":"21"}],["$","line","vw1qmm",{"x1":"12","x2":"12","y1":"17","y2":"21"}],"$undefined"]}],["$","div",null,{"children":[["$","p",null,{"className":"font-medium","children":"Prueba en Diferentes Tamaños"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Cambia el tamaño de la ventana del navegador o usa las herramientas de desarrollador para simular diferentes dispositivos."}]]}]]}]]}]}]]}]
c:["$","$Lf",null,{"children":["$L10",["$","$L11",null,{"promise":"$@12"}]]}]
d:["$","$1","h",{"children":[null,[["$","$L13",null,{"children":"$L14"}],null],["$","$L15",null,{"children":["$","div",null,{"hidden":true,"children":["$","$16",null,{"fallback":null,"children":"$L17"}]}]}]]}]
6:"$0:f:0:1:1:props:children:1:props:params"
14:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
10:null
12:{"metadata":[],"error":null,"digest":"$undefined"}
17:"$12:metadata"
