(()=>{var a={};a.id=942,a.ids=[942],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26286:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["parqueadero",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,72373)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\parqueadero\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(c.bind(c,29414)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\parqueadero\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\parqueadero\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/parqueadero/page",pathname:"/parqueadero",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/parqueadero/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29414:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(37413),e=c(54781),f=c(51358);function g(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(e.E,{className:"h-8 w-64"}),(0,d.jsx)(e.E,{className:"h-4 w-96 mt-2"})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(e.E,{className:"h-10 w-40"}),(0,d.jsx)(e.E,{className:"h-10 w-40"})]})]}),(0,d.jsx)("div",{className:"grid gap-4 md:grid-cols-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(e.E,{className:"h-4 w-32"}),(0,d.jsx)(e.E,{className:"h-4 w-4"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)(e.E,{className:"h-8 w-16"}),(0,d.jsx)(e.E,{className:"h-3 w-20 mt-2"})]})]},b))}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"flex space-x-1",children:Array.from({length:4}).map((a,b)=>(0,d.jsx)(e.E,{className:"h-10 w-24"},b))}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(e.E,{className:"h-6 w-48"}),(0,d.jsx)(e.E,{className:"h-4 w-64"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:Array.from({length:6}).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(e.E,{className:"h-12 w-12"}),(0,d.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,d.jsx)(e.E,{className:"h-4 w-32"}),(0,d.jsx)(e.E,{className:"h-3 w-24"})]}),(0,d.jsx)(e.E,{className:"h-8 w-20"}),(0,d.jsx)(e.E,{className:"h-8 w-16"})]},b))})})]})]})]})}},33873:a=>{"use strict";a.exports=require("path")},38940:(a,b,c)=>{Promise.resolve().then(c.bind(c,84271))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43649:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},51358:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(37413),e=c(61120),f=c(66819);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},54781:(a,b,c)=>{"use strict";c.d(b,{E:()=>f});var d=c(37413),e=c(66819);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66819:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}},68860:(a,b,c)=>{Promise.resolve().then(c.bind(c,72373))},72373:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\parqueadero\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\parqueadero\\page.tsx","default")},78335:()=>{},84271:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>x});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(68988),i=c(39390),j=c(59821),k=c(85910),l=c(96752),m=c(37826),n=c(63974),o=c(15616),p=c(43649),q=c(96474),r=c(94478),s=c(97992),t=c(41312),u=c(23928),v=c(82679),w=c(5336);function x(){let[a,b]=(0,e.useState)([{id:"VEH001",licensePlate:"ABC123",brand:"Toyota",model:"Corolla",color:"Blanco",type:"Autom\xf3vil",owner:"Juan P\xe9rez",room:"205",guestType:"guest",assignedSpace:"A-15",entryTime:"2024-01-15 14:30",exitTime:null,status:"parqueado",notes:""},{id:"VEH002",licensePlate:"XYZ789",brand:"Honda",model:"Civic",color:"Negro",type:"Autom\xf3vil",owner:"Mar\xeda Garc\xeda",room:"312",guestType:"guest",assignedSpace:"B-08",entryTime:"2024-01-15 16:45",exitTime:null,status:"parqueado",notes:""},{id:"VEH003",licensePlate:"DEF456",brand:"Chevrolet",model:"Spark",color:"Rojo",type:"Autom\xf3vil",owner:"Carlos L\xf3pez",room:null,guestType:"visitante",assignedSpace:"C-03",entryTime:"2024-01-15 10:15",exitTime:"2024-01-15 18:30",status:"salido",notes:"Visitante restaurante"}]),[c,x]=(0,e.useState)([{id:"ESP001",code:"A-15",zone:"Zona A",type:"Hu\xe9spedes",status:"ocupado",currentVehicle:"ABC123",hourlyRate:3e3,location:"Piso 1 - Sector Norte"},{id:"ESP002",code:"B-08",zone:"Zona B",type:"Hu\xe9spedes",status:"ocupado",currentVehicle:"XYZ789",hourlyRate:3e3,location:"Piso 1 - Sector Sur"},{id:"ESP003",code:"C-03",zone:"Zona C",type:"Visitantes",status:"disponible",currentVehicle:null,hourlyRate:5e3,location:"Piso 2 - Sector Norte"},{id:"ESP004",code:"D-12",zone:"Zona D",type:"Empleados",status:"disponible",currentVehicle:null,hourlyRate:0,location:"Piso S\xf3tano"},{id:"ESP005",code:"E-01",zone:"Zona E",type:"Carga/Descarga",status:"mantenimiento",currentVehicle:null,hourlyRate:0,location:"Piso 1 - Acceso Principal"}]),[y,z]=(0,e.useState)([{id:"INC001",type:"Da\xf1o Veh\xedculo",description:"Ray\xf3n en puerta lateral derecha",vehicle:"ABC123",space:"A-15",reportDate:"2024-01-15",status:"pendiente",responsible:"Seguridad",priority:"media"},{id:"INC002",type:"Infraestructura",description:"Luz del sector B no funciona",vehicle:null,space:"B-08",reportDate:"2024-01-14",status:"en_proceso",responsible:"Mantenimiento",priority:"alta"}]),[A,B]=(0,e.useState)({licensePlate:"",brand:"",model:"",color:"",type:"",owner:"",room:"",guestType:"",assignedSpace:"",notes:""}),[C,D]=(0,e.useState)({type:"",description:"",vehicle:"",space:"",priority:""}),[E,F]=(0,e.useState)([{date:"2024-01-15",occupied:95,available:25,maintenance:0,occupancyPercentage:79.2,revenue:285e3,vehiclesEntered:32,vehiclesExited:30},{date:"2024-01-14",occupied:88,available:32,maintenance:0,occupancyPercentage:73.3,revenue:245e3,vehiclesEntered:28,vehiclesExited:29},{date:"2024-01-13",occupied:102,available:18,maintenance:0,occupancyPercentage:85,revenue:32e4,vehiclesEntered:35,vehiclesExited:33}]),G=a=>{if("guest"===a.guestType||"employee"===a.guestType)return 0;let b=c.find(b=>b.code===a.assignedSpace);if(!b)return 0;let d=new Date(a.entryTime);return Math.ceil(((a.exitTime?new Date(a.exitTime):new Date).getTime()-d.getTime())/36e5)*b.hourlyRate};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Gesti\xf3n de Parqueadero"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Control de veh\xedculos, espacios e incidents"})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(m.lG,{children:[(0,d.jsx)(m.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{variant:"outline",children:[(0,d.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Reportar Incidencia"]})}),(0,d.jsxs)(m.Cf,{children:[(0,d.jsxs)(m.c7,{children:[(0,d.jsx)(m.L3,{children:"Reportar Incidencia"}),(0,d.jsx)(m.rr,{children:"Registrar una nueva incidencia en el parqueadero"})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"tipoIncidencia",children:"Tipo de Incidencia"}),(0,d.jsxs)(n.l6,{value:C.type,onValueChange:a=>D({...C,type:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar tipo"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"Da\xf1o Veh\xedculo",children:"Da\xf1o Veh\xedculo"}),(0,d.jsx)(n.eb,{value:"Infraestructura",children:"Infraestructura"}),(0,d.jsx)(n.eb,{value:"Seguridad",children:"Seguridad"}),(0,d.jsx)(n.eb,{value:"Limpieza",children:"Limpieza"}),(0,d.jsx)(n.eb,{value:"Otro",children:"Otro"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"prioridadIncidencia",children:"Prioridad"}),(0,d.jsxs)(n.l6,{value:C.priority,onValueChange:a=>D({...C,priority:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar prioridad"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"alta",children:"Alta"}),(0,d.jsx)(n.eb,{value:"media",children:"Media"}),(0,d.jsx)(n.eb,{value:"baja",children:"Baja"})]})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"vehiculoIncidencia",children:"Veh\xedculo (Opcional)"}),(0,d.jsxs)(n.l6,{value:C.vehicle,onValueChange:a=>D({...C,vehicle:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar veh\xedculo"})}),(0,d.jsx)(n.gC,{children:a.filter(a=>"parqueado"===a.status).map(a=>(0,d.jsxs)(n.eb,{value:a.licensePlate,children:[a.licensePlate," - ",a.owner]},a.id))})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"espacioIncidencia",children:"Espacio (Opcional)"}),(0,d.jsxs)(n.l6,{value:C.space,onValueChange:a=>D({...C,space:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar espacio"})}),(0,d.jsx)(n.gC,{children:c.map(a=>(0,d.jsxs)(n.eb,{value:a.code,children:[a.code," - ",a.zone]},a.id))})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"incidentDescription",children:"Descripci\xf3n"}),(0,d.jsx)(o.T,{id:"incidentDescription",value:C.description,onChange:a=>D({...C,description:a.target.value}),placeholder:"Descripci\xf3n detallada de la incidencia..."})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsx)(g.$,{onClick:()=>{if(C.type&&C.description){let a={id:`INC${String(y.length+1).padStart(3,"0")}`,type:C.type,description:C.description,vehicle:C.vehicle,space:C.space,reportDate:new Date().toISOString().split("T")[0],status:"pendiente",responsible:"Usuario Actual",priority:C.priority};z([...y,a]),D({type:"",description:"",vehicle:"",space:"",priority:""})}},children:"Reportar Incidencia"})]})]})]})]}),(0,d.jsxs)(m.lG,{children:[(0,d.jsx)(m.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"Registrar Veh\xedculo"]})}),(0,d.jsxs)(m.Cf,{className:"max-w-2xl",children:[(0,d.jsxs)(m.c7,{children:[(0,d.jsx)(m.L3,{children:"Registrar Veh\xedculo"}),(0,d.jsx)(m.rr,{children:"Registrar ingreso de un nuevo veh\xedculo"})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"placa",children:"Placa"}),(0,d.jsx)(h.p,{id:"placa",value:A.licensePlate,onChange:a=>B({...A,licensePlate:a.target.value.toUpperCase()}),placeholder:"ABC123"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"marca",children:"Marca"}),(0,d.jsx)(h.p,{id:"marca",value:A.brand,onChange:a=>B({...A,brand:a.target.value}),placeholder:"Toyota"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"modelo",children:"Modelo"}),(0,d.jsx)(h.p,{id:"modelo",value:A.model,onChange:a=>B({...A,model:a.target.value}),placeholder:"Corolla"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"color",children:"Color"}),(0,d.jsx)(h.p,{id:"color",value:A.color,onChange:a=>B({...A,color:a.target.value}),placeholder:"Blanco"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"tipo",children:"Tipo de Veh\xedculo"}),(0,d.jsxs)(n.l6,{value:A.type,onValueChange:a=>B({...A,type:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar tipo"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"Autom\xf3vil",children:"Autom\xf3vil"}),(0,d.jsx)(n.eb,{value:"Camioneta",children:"Camioneta"}),(0,d.jsx)(n.eb,{value:"Motocicleta",children:"Motocicleta"}),(0,d.jsx)(n.eb,{value:"Cami\xf3n",children:"Cami\xf3n"}),(0,d.jsx)(n.eb,{value:"Otro",children:"Otro"})]})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"propietario",children:"Propietario"}),(0,d.jsx)(h.p,{id:"propietario",value:A.owner,onChange:a=>B({...A,owner:a.target.value}),placeholder:"Nombre del propietario"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"guestType",children:"Tipo"}),(0,d.jsxs)(n.l6,{value:A.guestType,onValueChange:a=>B({...A,guestType:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar tipo"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"guest",children:"Hu\xe9sped"}),(0,d.jsx)(n.eb,{value:"visitante",children:"Visitante"}),(0,d.jsx)(n.eb,{value:"employee",children:"Empleado"})]})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"room",children:"Habitaci\xf3n (Opcional)"}),(0,d.jsx)(h.p,{id:"room",value:A.room,onChange:a=>B({...A,room:a.target.value}),placeholder:"205"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"espacioAsignado",children:"Espacio Asignado"}),(0,d.jsxs)(n.l6,{value:A.assignedSpace,onValueChange:a=>B({...A,assignedSpace:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar espacio"})}),(0,d.jsx)(n.gC,{children:c.filter(a=>"disponible"===a.status).map(a=>(0,d.jsxs)(n.eb,{value:a.code,children:[a.code," - ",a.zone," (",a.type,")"]},a.id))})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"notes",children:"Observaciones"}),(0,d.jsx)(o.T,{id:"notes",value:A.notes,onChange:a=>B({...A,notes:a.target.value}),placeholder:"Observaciones adicionales..."})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsx)(g.$,{onClick:()=>{if(A.licensePlate&&A.owner&&A.assignedSpace){let d={id:`VEH${String(a.length+1).padStart(3,"0")}`,licensePlate:A.licensePlate.toUpperCase(),brand:A.brand,model:A.model,color:A.color,type:A.type,owner:A.owner,room:A.room,guestType:A.guestType,assignedSpace:A.assignedSpace,entryTime:new Date().toISOString().slice(0,16).replace("T"," "),exitTime:null,status:"parqueado",notes:A.notes};b([...a,d]),x(c.map(a=>a.code===A.assignedSpace?{...a,status:"ocupado",currentVehicle:A.licensePlate.toUpperCase()}:a)),B({licensePlate:"",brand:"",model:"",color:"",type:"",owner:"",room:"",guestType:"",assignedSpace:"",notes:""})}},children:"Registrar Veh\xedculo"})]})]})]})]})]})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Espacios Ocupados"}),(0,d.jsx)(r.A,{className:"h-4 w-4 text-blue-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:c.filter(a=>"ocupado"===a.status).length}),(0,d.jsxs)("p",{className:"text-xs text-muted-foreground",children:["De ",c.length," espacios"]})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Espacios Disponibles"}),(0,d.jsx)(s.A,{className:"h-4 w-4 text-green-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:c.filter(a=>"disponible"===a.status).length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Libres ahora"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Veh\xedculos Hoy"}),(0,d.jsx)(t.A,{className:"h-4 w-4 text-purple-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:a.filter(a=>"parqueado"===a.status).length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Actualmente parqueados"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Ingresos Hoy"}),(0,d.jsx)(u.A,{className:"h-4 w-4 text-orange-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsxs)("div",{className:"text-2xl font-bold",children:["$",a.filter(a=>"visitante"===a.guestType).reduce((a,b)=>a+G(b),0).toLocaleString()]}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Solo visitantes"})]})]})]}),(0,d.jsxs)(k.tU,{defaultValue:"vehiculos",className:"space-y-4",children:[(0,d.jsxs)(k.j7,{children:[(0,d.jsx)(k.Xi,{value:"vehiculos",children:"Veh\xedculos"}),(0,d.jsx)(k.Xi,{value:"espacios",children:"Espacios"}),(0,d.jsx)(k.Xi,{value:"incidents",children:"Incidencias"}),(0,d.jsx)(k.Xi,{value:"reportes",children:"Reportes"})]}),(0,d.jsx)(k.av,{value:"vehiculos",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Veh\xedculos en el Parqueadero"}),(0,d.jsx)(f.BT,{children:"Control de veh\xedculos registrados"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Veh\xedculo"}),(0,d.jsx)(l.nd,{children:"Propietario"}),(0,d.jsx)(l.nd,{children:"Tipo"}),(0,d.jsx)(l.nd,{children:"Espacio"}),(0,d.jsx)(l.nd,{children:"Tiempo"}),(0,d.jsx)(l.nd,{children:"Costo"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:a.map(e=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:e.licensePlate}),(0,d.jsxs)("div",{className:"text-sm text-muted-foreground",children:[e.brand," ",e.model," - ",e.color]})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:e.owner}),e.room&&(0,d.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Hab. ",e.room]})]})}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"guest":return(0,d.jsx)(j.E,{className:"bg-purple-100 text-purple-800",children:"Hu\xe9sped"});case"visitante":return(0,d.jsx)(j.E,{className:"bg-orange-100 text-orange-800",children:"Visitante"});case"employee":return(0,d.jsx)(j.E,{className:"bg-blue-100 text-blue-800",children:"Empleado"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(e.guestType)}),(0,d.jsx)(l.nA,{children:e.assignedSpace}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsx)("div",{children:((a,b)=>{let c=new Date(a),d=(b?new Date(b):new Date).getTime()-c.getTime(),e=Math.floor(d/36e5),f=Math.floor(d%36e5/6e4);return`${e}h ${f}m`})(e.entryTime,e.exitTime)}),(0,d.jsxs)("div",{className:"text-muted-foreground",children:["Ingreso: ",e.entryTime.split(" ")[1]]})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsx)("div",{className:"font-medium",children:"guest"===e.guestType||"employee"===e.guestType?"Gratis":`$${G(e).toLocaleString()}`})}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"parqueado":return(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Parqueado"});case"salido":return(0,d.jsx)(j.E,{className:"bg-gray-100 text-gray-800",children:"Salido"});case"reservado":return(0,d.jsx)(j.E,{className:"bg-blue-100 text-blue-800",children:"Reservado"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(e.status)}),(0,d.jsx)(l.nA,{children:(0,d.jsx)("div",{className:"flex space-x-2",children:"parqueado"===e.status&&(0,d.jsx)(g.$,{size:"sm",onClick:()=>(d=>{let e=a.find(a=>a.id===d);e&&(b(a.map(a=>a.id===d?{...a,status:"salido",exitTime:new Date().toISOString().slice(0,16).replace("T"," ")}:a)),x(c.map(a=>a.code===e.assignedSpace?{...a,status:"disponible",currentVehicle:null}:a)))})(e.id),children:"Registrar Salida"})})})]},e.id))})]})})]})}),(0,d.jsx)(k.av,{value:"espacios",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Mapa de Espacios"}),(0,d.jsx)(f.BT,{children:"Estado actual de los espacios de parqueadero"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"C\xf3digo"}),(0,d.jsx)(l.nd,{children:"Zona"}),(0,d.jsx)(l.nd,{children:"Tipo"}),(0,d.jsx)(l.nd,{children:"Ubicaci\xf3n"}),(0,d.jsx)(l.nd,{children:"Veh\xedculo Actual"}),(0,d.jsx)(l.nd,{children:"Tarifa/Hora"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:c.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{className:"font-medium",children:a.code}),(0,d.jsx)(l.nA,{children:a.zone}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(j.E,{variant:"outline",children:a.type})}),(0,d.jsx)(l.nA,{children:a.location}),(0,d.jsx)(l.nA,{children:a.currentVehicle?(0,d.jsx)("div",{className:"font-medium",children:a.currentVehicle}):(0,d.jsx)("span",{className:"text-muted-foreground",children:"-"})}),(0,d.jsx)(l.nA,{children:0===a.hourlyRate?"Gratis":`$${a.hourlyRate.toLocaleString()}`}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"disponible":return(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Disponible"});case"ocupado":return(0,d.jsx)(j.E,{className:"bg-red-100 text-red-800",children:"Ocupado"});case"reservado":return(0,d.jsx)(j.E,{className:"bg-blue-100 text-blue-800",children:"Reservado"});case"mantenimiento":return(0,d.jsx)(j.E,{className:"bg-yellow-100 text-yellow-800",children:"Mantenimiento"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(a.status)}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"flex space-x-2",children:["mantenimiento"===a.status&&(0,d.jsxs)(g.$,{size:"sm",variant:"outline",children:[(0,d.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Habilitar"]}),"disponible"===a.status&&(0,d.jsxs)(g.$,{size:"sm",variant:"outline",children:[(0,d.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Mantenimiento"]})]})})]},a.id))})]})})]})}),(0,d.jsx)(k.av,{value:"incidents",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Incidencias Reportadas"}),(0,d.jsx)(f.BT,{children:"Gesti\xf3n de incidents y problemas"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Tipo"}),(0,d.jsx)(l.nd,{children:"Descripci\xf3n"}),(0,d.jsx)(l.nd,{children:"Veh\xedculo/Espacio"}),(0,d.jsx)(l.nd,{children:"Fecha"}),(0,d.jsx)(l.nd,{children:"Responsable"}),(0,d.jsx)(l.nd,{children:"Prioridad"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:y.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{children:(0,d.jsx)(j.E,{variant:"outline",children:a.type})}),(0,d.jsx)(l.nA,{children:a.description}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"text-sm",children:[a.vehicle&&(0,d.jsxs)("div",{children:["Veh: ",a.vehicle]}),a.space&&(0,d.jsxs)("div",{children:["Esp: ",a.space]})]})}),(0,d.jsx)(l.nA,{children:a.reportDate}),(0,d.jsx)(l.nA,{children:a.responsible}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"alta":return(0,d.jsx)(j.E,{variant:"destructive",children:"Alta"});case"media":return(0,d.jsx)(j.E,{className:"bg-yellow-100 text-yellow-800",children:"Media"});case"baja":return(0,d.jsx)(j.E,{variant:"secondary",children:"Baja"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(a.priority)}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"pendiente":return(0,d.jsx)(j.E,{className:"bg-yellow-100 text-yellow-800",children:"Pendiente"});case"en_proceso":return(0,d.jsx)(j.E,{className:"bg-blue-100 text-blue-800",children:"En Proceso"});case"resuelto":return(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Resuelto"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(a.status)}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"flex space-x-2",children:["pendiente"===a.status&&(0,d.jsx)(g.$,{size:"sm",variant:"outline",children:"Asignar"}),"en_proceso"===a.status&&(0,d.jsxs)(g.$,{size:"sm",children:[(0,d.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Resolver"]})]})})]},a.id))})]})})]})}),(0,d.jsx)(k.av,{value:"reportes",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Reportes de Ocupaci\xf3n"}),(0,d.jsx)(f.BT,{children:"An\xe1lisis de uso y ocupaci\xf3n del parqueadero"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Fecha"}),(0,d.jsx)(l.nd,{children:"Ocupaci\xf3n"}),(0,d.jsx)(l.nd,{children:"Disponibles"}),(0,d.jsx)(l.nd,{children:"Mantenimiento"}),(0,d.jsx)(l.nd,{children:"% Ocupaci\xf3n"}),(0,d.jsx)(l.nd,{children:"Ingresos"}),(0,d.jsx)(l.nd,{children:"Movimientos"})]})}),(0,d.jsx)(l.BF,{children:E.map((a,b)=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{className:"font-medium",children:a.date}),(0,d.jsx)(l.nA,{children:a.occupied}),(0,d.jsx)(l.nA,{children:a.available}),(0,d.jsx)(l.nA,{children:a.maintenance}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:`h-2 rounded-full ${a.occupancyPercentage>80?"bg-red-600":a.occupancyPercentage>60?"bg-yellow-600":"bg-green-600"}`,style:{width:`${a.occupancyPercentage}%`}})}),(0,d.jsxs)("span",{className:"text-sm font-medium",children:[a.occupancyPercentage,"%"]})]})}),(0,d.jsxs)(l.nA,{children:["$",a.revenue.toLocaleString()]}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsxs)("div",{children:["Ingresos: ",a.vehiclesEntered]}),(0,d.jsxs)("div",{children:["Salidas: ",a.vehiclesExited]})]})})]},b))})]})})]})})]})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96487:()=>{},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[438,271,17,957,867],()=>b(b.s=26286));module.exports=c})();