(()=>{var a={};a.id=921,a.ids=[921],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9455:(a,b,c)=>{Promise.resolve().then(c.bind(c,16542))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16542:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\perfil\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\perfil\\page.tsx","default")},17703:(a,b,c)=>{Promise.resolve().then(c.bind(c,92014))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},40626:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["perfil",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,16542)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\perfil\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(c.bind(c,64852)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\perfil\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\perfil\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/perfil/page",pathname:"/perfil",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/perfil/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64852:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(37413),e=c(54781),f=c(51358);function g(){return(0,d.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(e.E,{className:"h-8 w-48"}),(0,d.jsx)(e.E,{className:"h-4 w-96"})]}),(0,d.jsx)(e.E,{className:"h-10 w-32"})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(e.E,{className:"h-10 w-full"}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(e.E,{className:"h-6 w-48"}),(0,d.jsx)(e.E,{className:"h-4 w-96"})]}),(0,d.jsxs)(f.Wu,{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(e.E,{className:"h-20 w-20 rounded-full"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(e.E,{className:"h-6 w-32"}),(0,d.jsx)(e.E,{className:"h-4 w-24"}),(0,d.jsx)(e.E,{className:"h-8 w-28"})]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:Array.from({length:6}).map((a,b)=>(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(e.E,{className:"h-4 w-24"}),(0,d.jsx)(e.E,{className:"h-10 w-full"})]},b))})]})]})]})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92014:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>G});var d=c(60687),e=c(43210),f=c(24934),g=c(68988),h=c(39390),i=c(15616),j=c(55192),k=c(85910),l=c(70373),m=c(59821),n=c(40214),o=c(63974),p=c(11860),q=c(8819),r=c(62688);let s=(0,r.A)("pen-line",[["path",{d:"M13 21h8",key:"1jsn5i"}],["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),t=(0,r.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var u=c(58869),v=c(41550),w=c(48340),x=c(40228),y=c(97992),z=c(99891),A=c(19959);let B=(0,r.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);var C=c(34318),D=c(40083),E=c(71702),F=c(50063);function G(){let{user:a,updateUser:b}=(0,F.A)(),[c,r]=(0,e.useState)(!1),[G,H]=(0,e.useState)({name:a?.name||"Admin Hotel",email:a?.email||"<EMAIL>",phone:"+****************",address:"123 Hotel Street, City, Country",birthDate:"1990-01-15",bio:"Administrador del sistema hotelero con m\xe1s de 5 a\xf1os de experiencia en la gesti\xf3n de operaciones hoteleras.",department:"Administraci\xf3n",position:"Gerente General",startDate:"2019-03-15",employeeId:"EMP001",permissions:["admin","reservations","billing","reports"],emailNotifications:!0,pushNotifications:!0,smsNotifications:!1,language:"es",timezone:"America/Mexico_City",currency:"MXN"});return(0,d.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Mi Perfil"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Gestiona tu informaci\xf3n personal y configuraci\xf3n de cuenta"})]}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(f.$,{variant:"outline",onClick:()=>{H({...G,name:a?.name||"Admin Hotel",email:a?.email||"<EMAIL>"}),r(!1)},children:[(0,d.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Cancelar"]}),(0,d.jsxs)(f.$,{onClick:()=>{b({name:G.name,email:G.email}),r(!1),(0,E.oR)({title:"Perfil actualizado",description:"Los cambios se han guardado correctamente."})},children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"Guardar"]})]}):(0,d.jsxs)(f.$,{onClick:()=>r(!0),children:[(0,d.jsx)(s,{className:"mr-2 h-4 w-4"}),"Editar Perfil"]})})]}),(0,d.jsxs)(k.tU,{defaultValue:"personal",className:"space-y-4",children:[(0,d.jsxs)(k.j7,{className:"grid w-full grid-cols-4",children:[(0,d.jsx)(k.Xi,{value:"personal",children:"Informaci\xf3n Personal"}),(0,d.jsx)(k.Xi,{value:"work",children:"Informaci\xf3n Laboral"}),(0,d.jsx)(k.Xi,{value:"security",children:"Seguridad"}),(0,d.jsx)(k.Xi,{value:"notifications",children:"Notificaciones"})]}),(0,d.jsx)(k.av,{value:"personal",className:"space-y-4",children:(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsx)(j.ZB,{children:"Informaci\xf3n Personal"}),(0,d.jsx)(j.BT,{children:"Actualiza tu informaci\xf3n personal y datos de contacto"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)(l.eu,{className:"h-20 w-20",children:[(0,d.jsx)(l.BK,{src:a?.avatar||"/placeholder.svg"}),(0,d.jsx)(l.q5,{className:"text-lg",children:G.name.split(" ").map(a=>a[0]).join("")})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(f.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(t,{className:"mr-2 h-4 w-4"}),"Cambiar Foto"]}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"JPG, GIF o PNG. M\xe1ximo 1MB."})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"name",children:"Nombre Completo"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(u.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"name",value:G.name,onChange:a=>H({...G,name:a.target.value}),disabled:!c,className:"pl-8"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"email",children:"Correo Electr\xf3nico"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(v.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"email",type:"email",value:G.email,onChange:a=>H({...G,email:a.target.value}),disabled:!c,className:"pl-8"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"phone",children:"Tel\xe9fono"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(w.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"phone",value:G.phone,onChange:a=>H({...G,phone:a.target.value}),disabled:!c,className:"pl-8"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"birthDate",children:"Fecha de Nacimiento"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(x.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"birthDate",type:"date",value:G.birthDate,onChange:a=>H({...G,birthDate:a.target.value}),disabled:!c,className:"pl-8"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"address",children:"Direcci\xf3n"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(y.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(g.p,{id:"address",value:G.address,onChange:a=>H({...G,address:a.target.value}),disabled:!c,className:"pl-8"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"bio",children:"Biograf\xeda"}),(0,d.jsx)(i.T,{id:"bio",value:G.bio,onChange:a=>H({...G,bio:a.target.value}),disabled:!c,rows:3})]})]})]})}),(0,d.jsx)(k.av,{value:"work",className:"space-y-4",children:(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsx)(j.ZB,{children:"Informaci\xf3n Laboral"}),(0,d.jsx)(j.BT,{children:"Detalles sobre tu posici\xf3n y responsabilidades en el hotel"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"employeeId",children:"ID de Empleado"}),(0,d.jsx)(g.p,{id:"employeeId",value:G.employeeId,disabled:!0})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"department",children:"Departamento"}),(0,d.jsx)(g.p,{id:"department",value:G.department,onChange:a=>H({...G,department:a.target.value}),disabled:!c})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"position",children:"Posici\xf3n"}),(0,d.jsx)(g.p,{id:"position",value:G.position,onChange:a=>H({...G,position:a.target.value}),disabled:!c})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"startDate",children:"Fecha de Inicio"}),(0,d.jsx)(g.p,{id:"startDate",type:"date",value:G.startDate,disabled:!0})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{children:"Permisos y Roles"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:G.permissions.map(a=>(0,d.jsxs)(m.E,{variant:"secondary",children:[(0,d.jsx)(z.A,{className:"mr-1 h-3 w-3"}),a]},a))})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{children:"Responsabilidades"}),(0,d.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm text-muted-foreground",children:[(0,d.jsx)("li",{children:"Supervisi\xf3n general de operaciones hoteleras"}),(0,d.jsx)("li",{children:"Gesti\xf3n de reservas y facturaci\xf3n"}),(0,d.jsx)("li",{children:"Coordinaci\xf3n con departamentos de limpieza y restaurante"}),(0,d.jsx)("li",{children:"An\xe1lisis de reportes y m\xe9tricas de rendimiento"}),(0,d.jsx)("li",{children:"Atenci\xf3n al cliente y resoluci\xf3n de problemas"})]})]})]})]})}),(0,d.jsx)(k.av,{value:"security",className:"space-y-4",children:(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsx)(j.ZB,{children:"Configuraci\xf3n de Seguridad"}),(0,d.jsx)(j.BT,{children:"Gestiona tu contrase\xf1a y configuraci\xf3n de seguridad"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(A.A,{className:"h-5 w-5 text-muted-foreground"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:"Contrase\xf1a"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"\xdaltima actualizaci\xf3n: hace 30 d\xedas"})]})]}),(0,d.jsx)(f.$,{variant:"outline",children:"Cambiar Contrase\xf1a"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(B,{className:"h-5 w-5 text-muted-foreground"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:"Autenticaci\xf3n de Dos Factores"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Agrega una capa extra de seguridad"})]})]}),(0,d.jsx)(f.$,{variant:"outline",children:"Configurar 2FA"})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"font-medium",children:"Sesiones Activas"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(C.A,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:"Chrome en Windows"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"************* • Activa ahora"})]})]}),(0,d.jsx)(m.E,{variant:"secondary",children:"Actual"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(B,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:"Safari en iPhone"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"************* • hace 2 horas"})]})]}),(0,d.jsx)(f.$,{variant:"ghost",size:"sm",children:(0,d.jsx)(D.A,{className:"h-4 w-4"})})]})]})]})]})]})}),(0,d.jsx)(k.av,{value:"notifications",className:"space-y-4",children:(0,d.jsxs)(j.Zp,{children:[(0,d.jsxs)(j.aR,{children:[(0,d.jsx)(j.ZB,{children:"Preferencias de Notificaciones"}),(0,d.jsx)(j.BT,{children:"Configura c\xf3mo y cu\xe1ndo quieres recibir notificaciones"})]}),(0,d.jsxs)(j.Wu,{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(h.J,{children:"Notificaciones por Email"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Recibe actualizaciones importantes por correo"})]}),(0,d.jsx)(n.d,{checked:G.emailNotifications,onCheckedChange:a=>H({...G,emailNotifications:a})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(h.J,{children:"Notificaciones Push"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Recibe notificaciones en tiempo real"})]}),(0,d.jsx)(n.d,{checked:G.pushNotifications,onCheckedChange:a=>H({...G,pushNotifications:a})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(h.J,{children:"Notificaciones SMS"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Recibe alertas cr\xedticas por mensaje de texto"})]}),(0,d.jsx)(n.d,{checked:G.smsNotifications,onCheckedChange:a=>H({...G,smsNotifications:a})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"font-medium",children:"Preferencias Regionales"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"language",children:"Idioma"}),(0,d.jsxs)(o.l6,{value:G.language,onValueChange:a=>H({...G,language:a}),children:[(0,d.jsx)(o.bq,{children:(0,d.jsx)(o.yv,{})}),(0,d.jsxs)(o.gC,{children:[(0,d.jsx)(o.eb,{value:"es",children:"Espa\xf1ol"}),(0,d.jsx)(o.eb,{value:"en",children:"English"}),(0,d.jsx)(o.eb,{value:"fr",children:"Fran\xe7ais"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"timezone",children:"Zona Horaria"}),(0,d.jsxs)(o.l6,{value:G.timezone,onValueChange:a=>H({...G,timezone:a}),children:[(0,d.jsx)(o.bq,{children:(0,d.jsx)(o.yv,{})}),(0,d.jsxs)(o.gC,{children:[(0,d.jsx)(o.eb,{value:"America/Mexico_City",children:"Ciudad de M\xe9xico"}),(0,d.jsx)(o.eb,{value:"America/New_York",children:"Nueva York"}),(0,d.jsx)(o.eb,{value:"Europe/Madrid",children:"Madrid"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"currency",children:"Moneda"}),(0,d.jsxs)(o.l6,{value:G.currency,onValueChange:a=>H({...G,currency:a}),children:[(0,d.jsx)(o.bq,{children:(0,d.jsx)(o.yv,{})}),(0,d.jsxs)(o.gC,{children:[(0,d.jsx)(o.eb,{value:"MXN",children:"Peso Mexicano (MXN)"}),(0,d.jsx)(o.eb,{value:"USD",children:"D\xf3lar Americano (USD)"}),(0,d.jsx)(o.eb,{value:"EUR",children:"Euro (EUR)"})]})]})]})]})]})]})]})})]})]})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[438,271,17,957,300],()=>b(b.s=40626));module.exports=c})();