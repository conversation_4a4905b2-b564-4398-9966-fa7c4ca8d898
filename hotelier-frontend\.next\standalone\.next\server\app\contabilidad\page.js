(()=>{var a={};a.id=829,a.ids=[829],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2041:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(92491),e=c(90812),f=c(27747),g=c(9920),h=c(84629),i=(0,d.gu)({chartName:"BarChart",GraphicalChild:e.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:f.W},{axisType:"yAxis",AxisComp:g.h}],formatAxisMap:h.pr})},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12640:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},15616:(a,b,c)=>{"use strict";c.d(b,{T:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:c,...b}));g.displayName="Textarea"},18911:(a,b,c)=>{Promise.resolve().then(c.bind(c,87514))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25468:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>H});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(68988),i=c(39390),j=c(59821),k=c(85910),l=c(96752),m=c(30920),n=c(2041),o=c(85168),p=c(27747),q=c(9920),r=c(90812),s=c(53892),t=c(69206),u=c(25679),v=c(61855),w=c(49481),x=c(37826),y=c(63974),z=c(15616),A=c(79410),B=c(96474),C=c(25541),D=c(12640),E=c(23928),F=c(85778),G=c(71702);function H(){let[a,b]=(0,e.useState)(!1),[c,H]=(0,e.useState)(!0),{toast:I}=(0,G.dj)(),[J,K]=(0,e.useState)([]),L=[{name:"N\xf3mina",value:35,fill:"hsl(var(--chart-1))"},{name:"Servicios",value:25,fill:"hsl(var(--chart-2))"},{name:"Mantenimiento",value:15,fill:"hsl(var(--chart-3))"},{name:"Marketing",value:10,fill:"hsl(var(--chart-4))"},{name:"Otros",value:15,fill:"hsl(var(--chart-5))"}],M={revenue:{label:"Ingresos"},expenses:{label:"Gastos"},cashFlow:{label:"Flujo de Efectivo"},payroll:{label:"N\xf3mina"},services:{label:"Servicios"},maintenance:{label:"Mantenimiento"},marketing:{label:"Marketing"},other:{label:"Otros"}},[N,O]=(0,e.useState)([{id:"CTA001",code:"1105",name:"Caja General",type:"Activo",category:"Activo Corriente",balance:1542e4,transactions:156,lastTransaction:"2024-01-15"},{id:"CTA002",code:"1110",name:"Bancos",type:"Activo",category:"Activo Corriente",balance:4568e4,transactions:89,lastTransaction:"2024-01-15"},{id:"CTA003",code:"4135",name:"Ingresos por Hospedaje",type:"Ingreso",category:"Ingresos Operacionales",balance:125e6,transactions:234,lastTransaction:"2024-01-15"},{id:"CTA004",code:"5105",name:"Gastos de Personal",type:"Gasto",category:"Gastos Operacionales",balance:35e6,transactions:45,lastTransaction:"2024-01-14"}]),[P,Q]=(0,e.useState)([{id:"ASI001",number:"001",date:"2024-01-15",description:"Registro de ingresos por hospedaje",debit:25e5,credit:25e5,status:"registrado",user:"Contador Principal",transactions:[{account:"1105 - Caja General",debit:25e5,credit:0},{account:"4135 - Ingresos por Hospedaje",debit:0,credit:25e5}]},{id:"ASI002",number:"002",date:"2024-01-15",description:"Pago de n\xf3mina enero",debit:15e6,credit:15e6,status:"registrado",user:"Contador Principal",transactions:[{account:"5105 - Gastos de Personal",debit:15e6,credit:0},{account:"1110 - Bancos",debit:0,credit:15e6}]}]),[R,S]=(0,e.useState)([{category:"Ingresos por Hospedaje",budgeted:12e7,executed:125e6,variation:5e6,executionPercentage:104.2},{category:"Gastos de Personal",budgeted:4e7,executed:35e6,variation:-5e6,executionPercentage:87.5},{category:"Gastos Operacionales",budgeted:3e7,executed:28e6,variation:-2e6,executionPercentage:93.3},{category:"Gastos de Marketing",budgeted:15e6,executed:18e6,variation:3e6,executionPercentage:120}]),[T,U]=(0,e.useState)({description:"",transactions:[{account:"",debit:"",credit:""},{account:"",debit:"",credit:""}]}),[V,W]=(0,e.useState)({code:"",name:"",type:"",category:""}),X=(a,b,c)=>{let d=T.transactions.map((d,e)=>e===a?{...d,[b]:c}:d);U({...T,transactions:d})};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Gesti\xf3n Contable"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Contabilidad, reportes financieros y presupuesto"})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(x.lG,{children:[(0,d.jsx)(x.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{variant:"outline",children:[(0,d.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"Nueva Cuenta"]})}),(0,d.jsxs)(x.Cf,{children:[(0,d.jsxs)(x.c7,{children:[(0,d.jsx)(x.L3,{children:"Nueva Cuenta Contable"}),(0,d.jsx)(x.rr,{children:"Crear una nueva cuenta en el plan contable"})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"codigo",children:"C\xf3digo"}),(0,d.jsx)(h.p,{id:"codigo",value:V.code,onChange:a=>W({...V,code:a.target.value}),placeholder:"1105"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"tipo",children:"Tipo"}),(0,d.jsxs)(y.l6,{value:V.type,onValueChange:a=>W({...V,type:a}),children:[(0,d.jsx)(y.bq,{children:(0,d.jsx)(y.yv,{placeholder:"Seleccionar tipo"})}),(0,d.jsxs)(y.gC,{children:[(0,d.jsx)(y.eb,{value:"Activo",children:"Activo"}),(0,d.jsx)(y.eb,{value:"Pasivo",children:"Pasivo"}),(0,d.jsx)(y.eb,{value:"Patrimonio",children:"Patrimonio"}),(0,d.jsx)(y.eb,{value:"Ingreso",children:"Ingreso"}),(0,d.jsx)(y.eb,{value:"Gasto",children:"Gasto"})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"nombre",children:"Nombre de la Cuenta"}),(0,d.jsx)(h.p,{id:"nombre",value:V.name,onChange:a=>W({...V,name:a.target.value}),placeholder:"Nombre de la cuenta"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"category",children:"Categor\xeda"}),(0,d.jsx)(h.p,{id:"category",value:V.category,onChange:a=>W({...V,category:a.target.value}),placeholder:"Categor\xeda de la cuenta"})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsx)(g.$,{onClick:()=>{if(V.code&&V.name&&V.type){let a={id:`CTA${String(N.length+1).padStart(3,"0")}`,code:V.code,name:V.name,type:V.type,category:V.category,balance:0,transactions:0,lastTransaction:new Date().toISOString().split("T")[0]};O([...N,a]),W({code:"",name:"",type:"",category:""})}},children:"Crear Cuenta"})]})]})]})]}),(0,d.jsxs)(x.lG,{children:[(0,d.jsx)(x.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{children:[(0,d.jsx)(B.A,{className:"mr-2 h-4 w-4"}),"Nuevo Asiento"]})}),(0,d.jsxs)(x.Cf,{className:"max-w-4xl",children:[(0,d.jsxs)(x.c7,{children:[(0,d.jsx)(x.L3,{children:"Nuevo Asiento Contable"}),(0,d.jsx)(x.rr,{children:"Registrar un nuevo asiento contable"})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"description",children:"Descripci\xf3n"}),(0,d.jsx)(z.T,{id:"description",value:T.description,onChange:a=>U({...T,description:a.target.value}),placeholder:"Descripci\xf3n del asiento contable..."})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)(i.J,{children:"Movimientos Contables"}),(0,d.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>{U({...T,transactions:[...T.transactions,{account:"",debit:"",credit:""}]})},children:[(0,d.jsx)(B.A,{className:"mr-2 h-4 w-4"}),"Agregar Movimiento"]})]}),(0,d.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,d.jsxs)("div",{className:"grid grid-cols-4 gap-2 text-sm font-medium",children:[(0,d.jsx)("div",{children:"Cuenta"}),(0,d.jsx)("div",{children:"D\xe9bito"}),(0,d.jsx)("div",{children:"Cr\xe9dito"}),(0,d.jsx)("div",{})]}),T.transactions.map((a,b)=>(0,d.jsxs)("div",{className:"grid grid-cols-4 gap-2",children:[(0,d.jsxs)(y.l6,{value:a.account,onValueChange:a=>X(b,"account",a),children:[(0,d.jsx)(y.bq,{children:(0,d.jsx)(y.yv,{placeholder:"Seleccionar cuenta"})}),(0,d.jsx)(y.gC,{children:N.map(a=>(0,d.jsxs)(y.eb,{value:`${a.code} - ${a.name}`,children:[a.code," - ",a.name]},a.id))})]}),(0,d.jsx)(h.p,{type:"number",placeholder:"0",value:a.debit,onChange:a=>X(b,"debito",a.target.value)}),(0,d.jsx)(h.p,{type:"number",placeholder:"0",value:a.credit,onChange:a=>X(b,"credito",a.target.value)}),(0,d.jsx)(g.$,{variant:"outline",size:"sm",onClick:()=>{let a=T.transactions.filter((a,c)=>c!==b);U({...T,transactions:a})},children:"Eliminar"})]},b)),(0,d.jsxs)("div",{className:"grid grid-cols-4 gap-2 pt-2 border-t",children:[(0,d.jsx)("div",{className:"font-medium",children:"Totales:"}),(0,d.jsxs)("div",{className:"font-medium",children:["$",T.transactions.reduce((a,b)=>a+(Number.parseFloat(b.debit)||0),0).toLocaleString()]}),(0,d.jsxs)("div",{className:"font-medium",children:["$",T.transactions.reduce((a,b)=>a+(Number.parseFloat(b.credit)||0),0).toLocaleString()]}),(0,d.jsx)("div",{})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsx)(g.$,{onClick:()=>{if(T.description&&T.transactions.length>=2){let a=T.transactions.reduce((a,b)=>a+(Number.parseFloat(b.debit)||0),0),b=T.transactions.reduce((a,b)=>a+(Number.parseFloat(b.credit)||0),0);a===b&&a>0&&(Q([{id:`ASI${String(P.length+1).padStart(3,"0")}`,number:String(P.length+1).padStart(3,"0"),date:new Date().toISOString().split("T")[0],description:T.description,debit:a,credit:b,status:"registrado",user:"Usuario Actual",transactions:T.transactions.filter(a=>a.account&&(a.debit||a.credit)).map(a=>({account:a.account,debit:Number.parseFloat(a.debit)||0,credit:Number.parseFloat(a.credit)||0}))},...P]),U({description:"",transactions:[{account:"",debit:"",credit:""},{account:"",debit:"",credit:""}]}))}},children:"Registrar Asiento"})]})]})]})]})]})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Ingresos del Mes"}),(0,d.jsx)(C.A,{className:"h-4 w-4 text-green-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"$125,000,000"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"+8.2% vs mes anterior"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Gastos del Mes"}),(0,d.jsx)(D.A,{className:"h-4 w-4 text-red-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"$85,000,000"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"-2.1% vs mes anterior"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Utilidad Neta"}),(0,d.jsx)(E.A,{className:"h-4 w-4 text-blue-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"$32,400,000"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Margen: 25.9%"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Efectivo Disponible"}),(0,d.jsx)(F.A,{className:"h-4 w-4 text-purple-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"$61,100,000"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Caja + Bancos"})]})]})]}),a&&(0,d.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,d.jsxs)(f.Zp,{className:"lg:col-span-2",children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Ingresos vs Gastos"}),(0,d.jsx)(f.BT,{children:"Comparaci\xf3n mensual de ingresos y gastos"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)(m.at,{config:M,className:"h-[300px]",children:(0,d.jsxs)(n.E,{data:[{month:"Ene",revenue:125e6,expenses:85e6},{month:"Feb",revenue:135e6,expenses:9e7},{month:"Mar",revenue:145e6,expenses:95e6},{month:"Abr",revenue:155e6,expenses:1e8},{month:"May",revenue:165e6,expenses:105e6},{month:"Jun",revenue:175e6,expenses:11e7},{month:"Jul",revenue:185e6,expenses:115e6}],children:[(0,d.jsx)(o.d,{strokeDasharray:"3 3"}),(0,d.jsx)(p.W,{dataKey:"mes"}),(0,d.jsx)(q.h,{tickFormatter:a=>`$${(a/1e6).toFixed(0)}M`}),(0,d.jsx)(m.II,{content:(0,d.jsx)(m.Nt,{}),formatter:a=>[`$${(Number(a)/1e6).toFixed(1)}M`,""]}),(0,d.jsx)(r.y,{dataKey:"ingresos",fill:"hsl(var(--chart-1))"}),(0,d.jsx)(r.y,{dataKey:"gastos",fill:"hsl(var(--chart-4))"})]})})})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Distribuci\xf3n de Gastos"}),(0,d.jsx)(f.BT,{children:"Por categor\xeda"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)(m.at,{config:M,className:"mx-auto aspect-square max-h-[250px]",children:(0,d.jsxs)(s.r,{children:[(0,d.jsx)(m.II,{cursor:!1,content:(0,d.jsx)(m.Nt,{hideLabel:!0})}),(0,d.jsx)(t.F,{data:L,dataKey:"value",nameKey:"name",innerRadius:50,strokeWidth:5,children:L.map((a,b)=>(0,d.jsx)(u.f,{fill:a.fill},`cell-${b}`))})]})}),(0,d.jsx)("div",{className:"flex flex-wrap justify-center gap-2 mt-4",children:L.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:a.fill}}),(0,d.jsxs)("span",{className:"text-xs",children:[a.name," (",a.value,"%)"]})]},b))})]})]}),(0,d.jsxs)(f.Zp,{className:"lg:col-span-3",children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Flujo de Efectivo"}),(0,d.jsx)(f.BT,{children:"Evoluci\xf3n del flujo de efectivo mensual"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)(m.at,{config:M,className:"h-[200px]",children:(0,d.jsxs)(v.Q,{data:[{month:"Ene",cashFlow:4e7},{month:"Feb",cashFlow:45e6},{month:"Mar",cashFlow:5e7},{month:"Abr",cashFlow:55e6},{month:"May",cashFlow:6e7},{month:"Jun",cashFlow:65e6},{month:"Jul",cashFlow:7e7}],children:[(0,d.jsx)(o.d,{strokeDasharray:"3 3"}),(0,d.jsx)(p.W,{dataKey:"mes"}),(0,d.jsx)(q.h,{tickFormatter:a=>`$${(a/1e6).toFixed(0)}M`}),(0,d.jsx)(m.II,{content:(0,d.jsx)(m.Nt,{}),formatter:a=>[`$${(Number(a)/1e6).toFixed(1)}M`,"Flujo"]}),(0,d.jsx)(w.G,{type:"monotone",dataKey:"flujo",stroke:"hsl(var(--chart-2))",fill:"hsl(var(--chart-2))",fillOpacity:.3})]})})})]})]}),(0,d.jsxs)(k.tU,{defaultValue:"cuentas",className:"space-y-4",children:[(0,d.jsxs)(k.j7,{children:[(0,d.jsx)(k.Xi,{value:"cuentas",children:"Plan de Cuentas"}),(0,d.jsx)(k.Xi,{value:"asientos",children:"Asientos Contables"}),(0,d.jsx)(k.Xi,{value:"reportes",children:"Reportes Financieros"}),(0,d.jsx)(k.Xi,{value:"presupuesto",children:"Presupuesto"})]}),(0,d.jsx)(k.av,{value:"cuentas",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Plan de Cuentas"}),(0,d.jsx)(f.BT,{children:"Estructura contable del hotel"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"C\xf3digo"}),(0,d.jsx)(l.nd,{children:"Nombre"}),(0,d.jsx)(l.nd,{children:"Tipo"}),(0,d.jsx)(l.nd,{children:"Categor\xeda"}),(0,d.jsx)(l.nd,{children:"Saldo"}),(0,d.jsx)(l.nd,{children:"Movimientos"}),(0,d.jsx)(l.nd,{children:"\xdaltimo Mov."})]})}),(0,d.jsx)(l.BF,{children:N.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{className:"font-medium",children:a.code}),(0,d.jsx)(l.nA,{children:a.name}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"Activo":return(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Activo"});case"Pasivo":return(0,d.jsx)(j.E,{className:"bg-red-100 text-red-800",children:"Pasivo"});case"Patrimonio":return(0,d.jsx)(j.E,{className:"bg-blue-100 text-blue-800",children:"Patrimonio"});case"Ingreso":return(0,d.jsx)(j.E,{className:"bg-purple-100 text-purple-800",children:"Ingreso"});case"Gasto":return(0,d.jsx)(j.E,{className:"bg-orange-100 text-orange-800",children:"Gasto"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(a.type)}),(0,d.jsx)(l.nA,{children:a.category}),(0,d.jsxs)(l.nA,{className:"font-medium",children:["$",a.balance.toLocaleString()]}),(0,d.jsx)(l.nA,{children:a.transactions}),(0,d.jsx)(l.nA,{children:a.lastTransaction})]},a.id))})]})})]})}),(0,d.jsx)(k.av,{value:"asientos",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Libro Diario"}),(0,d.jsx)(f.BT,{children:"Registro de asientos contables"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:P.map(a=>(0,d.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"font-medium",children:["Asiento No. ",a.number]}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.date}),(0,d.jsx)("div",{className:"text-sm",children:a.description})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Por: ",a.user]}),(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Registrado"})]})]}),(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Cuenta"}),(0,d.jsx)(l.nd,{className:"text-right",children:"D\xe9bito"}),(0,d.jsx)(l.nd,{className:"text-right",children:"Cr\xe9dito"})]})}),(0,d.jsxs)(l.BF,{children:[a.transactions.map((a,b)=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{children:a.account}),(0,d.jsx)(l.nA,{className:"text-right",children:a.debit>0?`$${a.debit.toLocaleString()}`:"-"}),(0,d.jsx)(l.nA,{className:"text-right",children:a.credit>0?`$${a.credit.toLocaleString()}`:"-"})]},b)),(0,d.jsxs)(l.Hj,{className:"border-t-2",children:[(0,d.jsx)(l.nA,{className:"font-medium",children:"Totales:"}),(0,d.jsxs)(l.nA,{className:"text-right font-medium",children:["$",a.debit.toLocaleString()]}),(0,d.jsxs)(l.nA,{className:"text-right font-medium",children:["$",a.credit.toLocaleString()]})]})]})]})]},a.id))})})]})}),(0,d.jsx)(k.av,{value:"reportes",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Estado de Resultados"}),(0,d.jsx)(f.BT,{children:"An\xe1lisis de ingresos y gastos por per\xedodo"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Per\xedodo"}),(0,d.jsx)(l.nd,{children:"Ingresos"}),(0,d.jsx)(l.nd,{children:"Gastos"}),(0,d.jsx)(l.nd,{children:"Utilidad Bruta"}),(0,d.jsx)(l.nd,{children:"Impuestos"}),(0,d.jsx)(l.nd,{children:"Utilidad Neta"}),(0,d.jsx)(l.nd,{children:"Margen %"})]})}),(0,d.jsx)(l.BF,{children:J.map((a,b)=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{className:"font-medium",children:a.period}),(0,d.jsxs)(l.nA,{className:"text-green-600",children:["$",a.revenue.toLocaleString()]}),(0,d.jsxs)(l.nA,{className:"text-red-600",children:["$",a.expenses.toLocaleString()]}),(0,d.jsxs)(l.nA,{children:["$",a.grossProfit.toLocaleString()]}),(0,d.jsxs)(l.nA,{children:["$",a.taxes.toLocaleString()]}),(0,d.jsxs)(l.nA,{className:"font-medium",children:["$",a.netProfit.toLocaleString()]}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)(j.E,{className:a.profitMargin>25?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800",children:[a.profitMargin,"%"]})})]},b))})]})})]})}),(0,d.jsx)(k.av,{value:"presupuesto",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Control Presupuestal"}),(0,d.jsx)(f.BT,{children:"Comparaci\xf3n presupuesto vs ejecuci\xf3n"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Categor\xeda"}),(0,d.jsx)(l.nd,{children:"Presupuestado"}),(0,d.jsx)(l.nd,{children:"Ejecutado"}),(0,d.jsx)(l.nd,{children:"Variaci\xf3n"}),(0,d.jsx)(l.nd,{children:"% Ejecuci\xf3n"}),(0,d.jsx)(l.nd,{children:"Estado"})]})}),(0,d.jsx)(l.BF,{children:R.map((a,b)=>{var c;return(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{className:"font-medium",children:a.category}),(0,d.jsxs)(l.nA,{children:["$",a.budgeted.toLocaleString()]}),(0,d.jsxs)(l.nA,{children:["$",a.executed.toLocaleString()]}),(0,d.jsx)(l.nA,{children:(c=a.variation)>0?(0,d.jsxs)(j.E,{className:"bg-green-100 text-green-800",children:["+$",c.toLocaleString()]}):c<0?(0,d.jsxs)(j.E,{className:"bg-red-100 text-red-800",children:["$",c.toLocaleString()]}):(0,d.jsx)(j.E,{variant:"secondary",children:"$0"})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:`h-2 rounded-full ${a.executionPercentage>100?"bg-red-600":a.executionPercentage>90?"bg-yellow-600":"bg-green-600"}`,style:{width:`${Math.min(a.executionPercentage,100)}%`}})}),(0,d.jsxs)("span",{className:"text-sm font-medium",children:[a.executionPercentage,"%"]})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(j.E,{className:a.executionPercentage>110?"bg-red-100 text-red-800":a.executionPercentage>100?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800",children:a.executionPercentage>110?"Sobrepasado":a.executionPercentage>100?"Excedido":"En Rango"})})]},b)})})]})})]})})]})]})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27699:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(37413),e=c(54781),f=c(51358);function g(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(e.E,{className:"h-8 w-64"}),(0,d.jsx)(e.E,{className:"h-4 w-96 mt-2"})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(e.E,{className:"h-10 w-32"}),(0,d.jsx)(e.E,{className:"h-10 w-32"})]})]}),(0,d.jsx)("div",{className:"grid gap-4 md:grid-cols-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(e.E,{className:"h-4 w-32"}),(0,d.jsx)(e.E,{className:"h-4 w-4"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)(e.E,{className:"h-8 w-24"}),(0,d.jsx)(e.E,{className:"h-3 w-20 mt-2"})]})]},b))}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"flex space-x-1",children:Array.from({length:4}).map((a,b)=>(0,d.jsx)(e.E,{className:"h-10 w-32"},b))}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(e.E,{className:"h-6 w-48"}),(0,d.jsx)(e.E,{className:"h-4 w-64"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:Array.from({length:6}).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(e.E,{className:"h-4 w-32"}),(0,d.jsx)(e.E,{className:"h-3 w-24"})]}),(0,d.jsx)(e.E,{className:"h-8 w-20"}),(0,d.jsx)(e.E,{className:"h-8 w-24"})]},b))})})]})]})]})}},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},37826:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>m,L3:()=>o,c7:()=>n,lG:()=>i,rr:()=>p,zM:()=>j});var d=c(60687),e=c(43210),f=c(26134),g=c(11860),h=c(96241);let i=f.bL,j=f.l9,k=f.ZL;f.bm;let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hJ,{ref:c,className:(0,h.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...b}));l.displayName=f.hJ.displayName;let m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(k,{children:[(0,d.jsx)(l,{}),(0,d.jsxs)(f.UC,{ref:e,className:(0,h.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[b,(0,d.jsxs)(f.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=f.UC.displayName;let n=({className:a,...b})=>(0,d.jsx)("div",{className:(0,h.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...b});n.displayName="DialogHeader";let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hE,{ref:c,className:(0,h.cn)("text-lg font-semibold leading-none tracking-tight",a),...b}));o.displayName=f.hE.displayName;let p=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.VY,{ref:c,className:(0,h.cn)("text-sm text-muted-foreground",a),...b}));p.displayName=f.VY.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},51358:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(37413),e=c(61120),f=c(66819);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},54781:(a,b,c)=>{"use strict";c.d(b,{E:()=>f});var d=c(37413),e=c(66819);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}},55146:(a,b,c)=>{"use strict";c.d(b,{B8:()=>D,UC:()=>F,bL:()=>C,l9:()=>E});var d=c(43210),e=c(70569),f=c(11273),g=c(72942),h=c(46059),i=c(14163),j=c(43),k=c(65551),l=c(96963),m=c(60687),n="Tabs",[o,p]=(0,f.A)(n,[g.RG]),q=(0,g.RG)(),[r,s]=o(n),t=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,onValueChange:e,defaultValue:f,orientation:g="horizontal",dir:h,activationMode:o="automatic",...p}=a,q=(0,j.jH)(h),[s,t]=(0,k.i)({prop:d,onChange:e,defaultProp:f??"",caller:n});return(0,m.jsx)(r,{scope:c,baseId:(0,l.B)(),value:s,onValueChange:t,orientation:g,dir:q,activationMode:o,children:(0,m.jsx)(i.sG.div,{dir:q,"data-orientation":g,...p,ref:b})})});t.displayName=n;var u="TabsList",v=d.forwardRef((a,b)=>{let{__scopeTabs:c,loop:d=!0,...e}=a,f=s(u,c),h=q(c);return(0,m.jsx)(g.bL,{asChild:!0,...h,orientation:f.orientation,dir:f.dir,loop:d,children:(0,m.jsx)(i.sG.div,{role:"tablist","aria-orientation":f.orientation,...e,ref:b})})});v.displayName=u;var w="TabsTrigger",x=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,disabled:f=!1,...h}=a,j=s(w,c),k=q(c),l=A(j.baseId,d),n=B(j.baseId,d),o=d===j.value;return(0,m.jsx)(g.q7,{asChild:!0,...k,focusable:!f,active:o,children:(0,m.jsx)(i.sG.button,{type:"button",role:"tab","aria-selected":o,"aria-controls":n,"data-state":o?"active":"inactive","data-disabled":f?"":void 0,disabled:f,id:l,...h,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f||0!==a.button||!1!==a.ctrlKey?a.preventDefault():j.onValueChange(d)}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&j.onValueChange(d)}),onFocus:(0,e.m)(a.onFocus,()=>{let a="manual"!==j.activationMode;o||f||!a||j.onValueChange(d)})})})});x.displayName=w;var y="TabsContent",z=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,forceMount:f,children:g,...j}=a,k=s(y,c),l=A(k.baseId,e),n=B(k.baseId,e),o=e===k.value,p=d.useRef(o);return d.useEffect(()=>{let a=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,m.jsx)(h.C,{present:f||o,children:({present:c})=>(0,m.jsx)(i.sG.div,{"data-state":o?"active":"inactive","data-orientation":k.orientation,role:"tabpanel","aria-labelledby":l,hidden:!c,id:n,tabIndex:0,...j,ref:b,style:{...a.style,animationDuration:p.current?"0s":void 0},children:c&&g})})});function A(a,b){return`${a}-trigger-${b}`}function B(a,b){return`${a}-content-${b}`}z.displayName=y;var C=t,D=v,E=x,F=z},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66819:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}},67022:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["contabilidad",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,87514)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\contabilidad\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(c.bind(c,27699)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\contabilidad\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\contabilidad\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/contabilidad/page",pathname:"/contabilidad",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/contabilidad/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},79159:(a,b,c)=>{Promise.resolve().then(c.bind(c,25468))},79410:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},85778:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},85910:(a,b,c)=>{"use strict";c.d(b,{Xi:()=>j,av:()=>k,j7:()=>i,tU:()=>h});var d=c(60687),e=c(43210),f=c(55146),g=c(96241);let h=f.bL,i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.B8,{ref:c,className:(0,g.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...b}));i.displayName=f.B8.displayName;let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.l9,{ref:c,className:(0,g.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...b}));j.displayName=f.l9.displayName;let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.UC,{ref:c,className:(0,g.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...b}));k.displayName=f.UC.displayName},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87514:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\contabilidad\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\contabilidad\\page.tsx","default")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[438,271,17,436,938,957,274],()=>b(b.s=67022));module.exports=c})();