(()=>{var a={};a.id=678,a.ids=[678],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14295:(a,b,c)=>{Promise.resolve().then(c.bind(c,16077))},16077:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\(auth)\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\(auth)\\register\\page.tsx","default")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27470:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(37413);function e({children:a}){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/20 via-background to-secondary/20 p-4",children:a})}},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},53723:(a,b,c)=>{"use strict";c.d(b,{S:()=>E});var d=c(60687),e=c(43210),f=c(98599),g=c(11273),h=c(70569),i=c(65551),j=c(83721),k=c(18853),l=c(46059),m=c(14163),n="Checkbox",[o,p]=(0,g.A)(n),[q,r]=o(n);function s(a){let{__scopeCheckbox:b,checked:c,children:f,defaultChecked:g,disabled:h,form:j,name:k,onCheckedChange:l,required:m,value:o="on",internal_do_not_use_render:p}=a,[r,s]=(0,i.i)({prop:c,defaultProp:g??!1,onChange:l,caller:n}),[t,u]=e.useState(null),[v,w]=e.useState(null),x=e.useRef(!1),y=!t||!!j||!!t.closest("form"),z={checked:r,disabled:h,setChecked:s,control:t,setControl:u,name:k,form:j,value:o,hasConsumerStoppedPropagationRef:x,required:m,defaultChecked:!A(g)&&g,isFormControl:y,bubbleInput:v,setBubbleInput:w};return(0,d.jsx)(q,{scope:b,...z,children:"function"==typeof p?p(z):f})}var t="CheckboxTrigger",u=e.forwardRef(({__scopeCheckbox:a,onKeyDown:b,onClick:c,...g},i)=>{let{control:j,value:k,disabled:l,checked:n,required:o,setControl:p,setChecked:q,hasConsumerStoppedPropagationRef:s,isFormControl:u,bubbleInput:v}=r(t,a),w=(0,f.s)(i,p),x=e.useRef(n);return e.useEffect(()=>{let a=j?.form;if(a){let b=()=>q(x.current);return a.addEventListener("reset",b),()=>a.removeEventListener("reset",b)}},[j,q]),(0,d.jsx)(m.sG.button,{type:"button",role:"checkbox","aria-checked":A(n)?"mixed":n,"aria-required":o,"data-state":B(n),"data-disabled":l?"":void 0,disabled:l,value:k,...g,ref:w,onKeyDown:(0,h.m)(b,a=>{"Enter"===a.key&&a.preventDefault()}),onClick:(0,h.m)(c,a=>{q(a=>!!A(a)||!a),v&&u&&(s.current=a.isPropagationStopped(),s.current||a.stopPropagation())})})});u.displayName=t;var v=e.forwardRef((a,b)=>{let{__scopeCheckbox:c,name:e,checked:f,defaultChecked:g,required:h,disabled:i,value:j,onCheckedChange:k,form:l,...m}=a;return(0,d.jsx)(s,{__scopeCheckbox:c,checked:f,defaultChecked:g,disabled:i,required:h,onCheckedChange:k,name:e,form:l,value:j,internal_do_not_use_render:({isFormControl:a})=>(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(u,{...m,ref:b,__scopeCheckbox:c}),a&&(0,d.jsx)(z,{__scopeCheckbox:c})]})})});v.displayName=n;var w="CheckboxIndicator",x=e.forwardRef((a,b)=>{let{__scopeCheckbox:c,forceMount:e,...f}=a,g=r(w,c);return(0,d.jsx)(l.C,{present:e||A(g.checked)||!0===g.checked,children:(0,d.jsx)(m.sG.span,{"data-state":B(g.checked),"data-disabled":g.disabled?"":void 0,...f,ref:b,style:{pointerEvents:"none",...a.style}})})});x.displayName=w;var y="CheckboxBubbleInput",z=e.forwardRef(({__scopeCheckbox:a,...b},c)=>{let{control:g,hasConsumerStoppedPropagationRef:h,checked:i,defaultChecked:l,required:n,disabled:o,name:p,value:q,form:s,bubbleInput:t,setBubbleInput:u}=r(y,a),v=(0,f.s)(c,u),w=(0,j.Z)(i),x=(0,k.X)(g);e.useEffect(()=>{if(!t)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,b=!h.current;if(w!==i&&a){let c=new Event("click",{bubbles:b});t.indeterminate=A(i),a.call(t,!A(i)&&i),t.dispatchEvent(c)}},[t,w,i,h]);let z=e.useRef(!A(i)&&i);return(0,d.jsx)(m.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:l??z.current,required:n,disabled:o,name:p,value:q,form:s,...b,tabIndex:-1,ref:v,style:{...b.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(a){return"indeterminate"===a}function B(a){return A(a)?"indeterminate":a?"checked":"unchecked"}z.displayName=y;var C=c(13964),D=c(96241);let E=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(v,{ref:c,className:(0,D.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...b,children:(0,d.jsx)(x,{className:(0,D.cn)("flex items-center justify-center text-current"),children:(0,d.jsx)(C.A,{className:"h-4 w-4"})})}));E.displayName=v.displayName},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},59551:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>v});var d=c(60687),e=c(43210),f=c(24934),g=c(68988),h=c(39390),i=c(55192),j=c(53723),k=c(63974),l=c(58869),m=c(79410),n=c(97992),o=c(13861),p=c(12597),q=c(41862),r=c(25822),s=c(85814),t=c.n(s),u=c(16189);function v(){let a=(0,u.useRouter)(),[b,c]=(0,e.useState)(!1),[s,v]=(0,e.useState)(!1),[w,x]=(0,e.useState)(!1),[y,z]=(0,e.useState)(!1),[A,B]=(0,e.useState)({firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:"",company:"",position:"",country:"",city:"",hotelType:"",hotelSize:""}),C=(a,b)=>{B(c=>({...c,[a]:b}))},D=async b=>{if(b.preventDefault(),A.password!==A.confirmPassword)return void alert("Las contrase\xf1as no coinciden");if(!y)return void alert("Debes aceptar los t\xe9rminos y condiciones");c(!0);try{await new Promise(a=>setTimeout(a,2e3)),a.push("/login?message=Registro exitoso. Puedes iniciar sesi\xf3n.")}catch(a){console.error("Error en registro:",a),alert("Error al registrar usuario")}finally{c(!1)}};return(0,d.jsxs)(i.Zp,{className:"w-full max-w-2xl",children:[(0,d.jsxs)(i.aR,{className:"space-y-1 text-center",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(r.M,{variant:"modern",size:"lg",showBackground:!1}),(0,d.jsxs)("div",{className:"text-left",children:[(0,d.jsx)("span",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Hotelier"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Management Simplified"})]})]})}),(0,d.jsx)(i.ZB,{className:"text-2xl",children:"Crear Cuenta"}),(0,d.jsx)(i.BT,{children:"\xdanete a Hotelier y transforma la gesti\xf3n de tu hotel"})]}),(0,d.jsxs)(i.Wu,{children:[(0,d.jsxs)("form",{onSubmit:D,className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 text-primary"}),(0,d.jsx)("h3",{className:"text-sm font-medium",children:"Informaci\xf3n Personal"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"firstName",children:"Nombre"}),(0,d.jsx)(g.p,{id:"firstName",type:"text",placeholder:"Tu nombre",value:A.firstName,onChange:a=>C("firstName",a.target.value),required:!0,disabled:b})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"lastName",children:"Apellido"}),(0,d.jsx)(g.p,{id:"lastName",type:"text",placeholder:"Tu apellido",value:A.lastName,onChange:a=>C("lastName",a.target.value),required:!0,disabled:b})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"email",children:"Correo Electr\xf3nico"}),(0,d.jsx)(g.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:A.email,onChange:a=>C("email",a.target.value),required:!0,disabled:b})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"phone",children:"Tel\xe9fono"}),(0,d.jsx)(g.p,{id:"phone",type:"tel",placeholder:"+57 ************",value:A.phone,onChange:a=>C("phone",a.target.value),required:!0,disabled:b})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 text-primary"}),(0,d.jsx)("h3",{className:"text-sm font-medium",children:"Informaci\xf3n de tu Hotel"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"company",children:"Nombre del Hotel"}),(0,d.jsx)(g.p,{id:"company",type:"text",placeholder:"Hotel Plaza Real",value:A.company,onChange:a=>C("company",a.target.value),required:!0,disabled:b})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"position",children:"Tu Cargo"}),(0,d.jsx)(g.p,{id:"position",type:"text",placeholder:"Gerente General",value:A.position,onChange:a=>C("position",a.target.value),required:!0,disabled:b})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"hotelType",children:"Tipo de Hotel"}),(0,d.jsxs)(k.l6,{value:A.hotelType,onValueChange:a=>C("hotelType",a),disabled:b,children:[(0,d.jsx)(k.bq,{children:(0,d.jsx)(k.yv,{placeholder:"Seleccionar tipo"})}),(0,d.jsxs)(k.gC,{children:[(0,d.jsx)(k.eb,{value:"boutique",children:"Hotel Boutique"}),(0,d.jsx)(k.eb,{value:"business",children:"Hotel de Negocios"}),(0,d.jsx)(k.eb,{value:"resort",children:"Resort"}),(0,d.jsx)(k.eb,{value:"city",children:"Hotel Urbano"}),(0,d.jsx)(k.eb,{value:"economico",children:"Hotel Econ\xf3mico"}),(0,d.jsx)(k.eb,{value:"lujo",children:"Hotel de Lujo"}),(0,d.jsx)(k.eb,{value:"otro",children:"Otro"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"hotelSize",children:"Tama\xf1o del Hotel"}),(0,d.jsxs)(k.l6,{value:A.hotelSize,onValueChange:a=>C("hotelSize",a),disabled:b,children:[(0,d.jsx)(k.bq,{children:(0,d.jsx)(k.yv,{placeholder:"N\xfamero de habitaciones"})}),(0,d.jsxs)(k.gC,{children:[(0,d.jsx)(k.eb,{value:"pequeno",children:"1-25 habitaciones"}),(0,d.jsx)(k.eb,{value:"mediano",children:"26-75 habitaciones"}),(0,d.jsx)(k.eb,{value:"grande",children:"76-150 habitaciones"}),(0,d.jsx)(k.eb,{value:"muy-grande",children:"151-300 habitaciones"}),(0,d.jsx)(k.eb,{value:"mega",children:"M\xe1s de 300 habitaciones"})]})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 text-primary"}),(0,d.jsx)("h3",{className:"text-sm font-medium",children:"Ubicaci\xf3n"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"country",children:"Pa\xeds"}),(0,d.jsxs)(k.l6,{value:A.country,onValueChange:a=>C("country",a),disabled:b,children:[(0,d.jsx)(k.bq,{children:(0,d.jsx)(k.yv,{placeholder:"Seleccionar pa\xeds"})}),(0,d.jsxs)(k.gC,{children:[(0,d.jsx)(k.eb,{value:"colombia",children:"Colombia"}),(0,d.jsx)(k.eb,{value:"mexico",children:"M\xe9xico"}),(0,d.jsx)(k.eb,{value:"argentina",children:"Argentina"}),(0,d.jsx)(k.eb,{value:"chile",children:"Chile"}),(0,d.jsx)(k.eb,{value:"peru",children:"Per\xfa"}),(0,d.jsx)(k.eb,{value:"ecuador",children:"Ecuador"}),(0,d.jsx)(k.eb,{value:"venezuela",children:"Venezuela"}),(0,d.jsx)(k.eb,{value:"otro",children:"Otro"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"city",children:"Ciudad"}),(0,d.jsx)(g.p,{id:"city",type:"text",placeholder:"Bogot\xe1",value:A.city,onChange:a=>C("city",a.target.value),required:!0,disabled:b})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 text-primary"}),(0,d.jsx)("h3",{className:"text-sm font-medium",children:"Seguridad"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"password",children:"Contrase\xf1a"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(g.p,{id:"password",type:s?"text":"password",placeholder:"M\xednimo 8 caracteres",value:A.password,onChange:a=>C("password",a.target.value),required:!0,disabled:b,minLength:8}),(0,d.jsx)(f.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>v(!s),disabled:b,children:s?(0,d.jsx)(p.A,{className:"h-4 w-4"}):(0,d.jsx)(o.A,{className:"h-4 w-4"})})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"confirmPassword",children:"Confirmar Contrase\xf1a"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(g.p,{id:"confirmPassword",type:w?"text":"password",placeholder:"Repetir contrase\xf1a",value:A.confirmPassword,onChange:a=>C("confirmPassword",a.target.value),required:!0,disabled:b}),(0,d.jsx)(f.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>x(!w),disabled:b,children:w?(0,d.jsx)(p.A,{className:"h-4 w-4"}):(0,d.jsx)(o.A,{className:"h-4 w-4"})})]})]})]})]}),(0,d.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,d.jsx)(j.S,{id:"terms",checked:y,onCheckedChange:a=>z(a),disabled:b,className:"mt-1"}),(0,d.jsxs)(h.J,{htmlFor:"terms",className:"text-sm leading-relaxed",children:["Acepto los"," ",(0,d.jsx)(t(),{href:"/terms",className:"text-primary underline-offset-4 hover:underline",children:"t\xe9rminos y condiciones"})," ","y la"," ",(0,d.jsx)(t(),{href:"/privacy",className:"text-primary underline-offset-4 hover:underline",children:"pol\xedtica de privacidad"})]})]}),(0,d.jsx)(f.$,{type:"submit",className:"w-full",disabled:b||!y,children:b?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creando cuenta..."]}):"Crear Cuenta"})]}),(0,d.jsx)("div",{className:"mt-6 text-center",children:(0,d.jsxs)("p",{className:"text-sm text-muted-foreground",children:["\xbfYa tienes una cuenta?"," ",(0,d.jsx)(t(),{href:"/login",className:"text-primary underline-offset-4 hover:underline font-medium",children:"Inicia sesi\xf3n aqu\xed"})]})}),(0,d.jsxs)("div",{className:"mt-4 p-4 bg-muted rounded-lg",children:[(0,d.jsxs)("p",{className:"text-sm text-muted-foreground text-center mb-2",children:["✨ ",(0,d.jsx)("strong",{children:"Prueba gratis por 30 d\xedas"})]}),(0,d.jsx)("p",{className:"text-xs text-center text-muted-foreground",children:"Sin tarjeta de cr\xe9dito requerida • Cancela en cualquier momento"})]})]})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(a,b,c)=>{"use strict";c.d(b,{bq:()=>m,eb:()=>q,gC:()=>p,l6:()=>k,yv:()=>l});var d=c(60687),e=c(43210),f=c(72951),g=c(78272),h=c(3589),i=c(13964),j=c(96241);let k=f.bL;f.YJ;let l=f.WT,m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.l9,{ref:e,className:(0,j.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,d.jsx)(f.In,{asChild:!0,children:(0,d.jsx)(g.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=f.l9.displayName;let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.PP,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}));n.displayName=f.PP.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wn,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(g.A,{className:"h-4 w-4"})}));o.displayName=f.wn.displayName;let p=e.forwardRef(({className:a,children:b,position:c="popper",...e},g)=>(0,d.jsx)(f.ZL,{children:(0,d.jsxs)(f.UC,{ref:g,className:(0,j.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(n,{}),(0,d.jsx)(f.LM,{className:(0,j.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(o,{})]})}));p.displayName=f.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.JU,{ref:c,className:(0,j.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.q7,{ref:e,className:(0,j.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),(0,d.jsx)(f.p4,{children:b})]}));q.displayName=f.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=f.wv.displayName},67302:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(auth)",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,16077)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\(auth)\\register\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,27470)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\(auth)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\(auth)\\register\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(auth)/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(auth)/register/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},67439:(a,b,c)=>{Promise.resolve().then(c.bind(c,59551))},78335:()=>{},79410:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[438,271,957],()=>b(b.s=67302));module.exports=c})();