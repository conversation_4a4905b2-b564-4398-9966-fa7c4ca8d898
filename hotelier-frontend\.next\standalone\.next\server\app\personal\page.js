(()=>{var a={};a.id=27,a.ids=[27],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12589:(a,b,c)=>{Promise.resolve().then(c.bind(c,75340))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33135:(a,b,c)=>{"use strict";c.d(b,{AM:()=>h,Wv:()=>i,hl:()=>j});var d=c(60687),e=c(43210),f=c(40599),g=c(96241);let h=f.bL,i=f.l9,j=e.forwardRef(({className:a,align:b="center",sideOffset:c=4,...e},h)=>(0,d.jsx)(f.ZL,{children:(0,d.jsx)(f.UC,{ref:h,align:b,sideOffset:c,className:(0,g.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",a),...e})}));j.displayName=f.UC.displayName},33873:a=>{"use strict";a.exports=require("path")},37826:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>m,L3:()=>o,c7:()=>n,lG:()=>i,rr:()=>p,zM:()=>j});var d=c(60687),e=c(43210),f=c(26134),g=c(11860),h=c(96241);let i=f.bL,j=f.l9,k=f.ZL;f.bm;let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hJ,{ref:c,className:(0,h.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...b}));l.displayName=f.hJ.displayName;let m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(k,{children:[(0,d.jsx)(l,{}),(0,d.jsxs)(f.UC,{ref:e,className:(0,h.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[b,(0,d.jsxs)(f.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=f.UC.displayName;let n=({className:a,...b})=>(0,d.jsx)("div",{className:(0,h.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...b});n.displayName="DialogHeader";let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hE,{ref:c,className:(0,h.cn)("text-lg font-semibold leading-none tracking-tight",a),...b}));o.displayName=f.hE.displayName;let p=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.VY,{ref:c,className:(0,h.cn)("text-sm text-muted-foreground",a),...b}));p.displayName=f.VY.displayName},38380:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\personal\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\personal\\page.tsx","default")},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},51358:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(37413),e=c(61120),f=c(66819);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},54781:(a,b,c)=>{"use strict";c.d(b,{E:()=>f});var d=c(37413),e=c(66819);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},56924:(a,b,c)=>{"use strict";c.d(b,{V:()=>m});var d=c(60687),e=c(43210),f=c(47033),g=c(14952),h=c(78272),i=c(99471),j=c(60358),k=c(96241),l=c(24934);function m({className:a,classNames:b,showOutsideDays:c=!0,captionLayout:e="label",buttonVariant:m="ghost",formatters:o,components:p,...q}){let r=(0,i.a)();return(0,d.jsx)(j.h,{showOutsideDays:c,className:(0,k.cn)("bg-background group/calendar p-3 [--cell-size:2rem] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent",String.raw`rtl:**:[.rdp-button\_next>svg]:rotate-180`,String.raw`rtl:**:[.rdp-button\_previous>svg]:rotate-180`,a),captionLayout:e,formatters:{formatMonthDropdown:a=>a.toLocaleString("default",{month:"short"}),...o},classNames:{root:(0,k.cn)("w-fit",r.root),months:(0,k.cn)("relative flex flex-col gap-4 md:flex-row",r.months),month:(0,k.cn)("flex w-full flex-col gap-4",r.month),nav:(0,k.cn)("absolute inset-x-0 top-0 flex w-full items-center justify-between gap-1",r.nav),button_previous:(0,k.cn)((0,l.r)({variant:m}),"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50",r.button_previous),button_next:(0,k.cn)((0,l.r)({variant:m}),"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50",r.button_next),month_caption:(0,k.cn)("flex h-[--cell-size] w-full items-center justify-center px-[--cell-size]",r.month_caption),dropdowns:(0,k.cn)("flex h-[--cell-size] w-full items-center justify-center gap-1.5 text-sm font-medium",r.dropdowns),dropdown_root:(0,k.cn)("has-focus:border-ring border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] relative rounded-md border",r.dropdown_root),dropdown:(0,k.cn)("bg-popover absolute inset-0 opacity-0",r.dropdown),caption_label:(0,k.cn)("select-none font-medium","label"===e?"text-sm":"[&>svg]:text-muted-foreground flex h-8 items-center gap-1 rounded-md pl-2 pr-1 text-sm [&>svg]:size-3.5",r.caption_label),table:"w-full border-collapse",weekdays:(0,k.cn)("flex",r.weekdays),weekday:(0,k.cn)("text-muted-foreground flex-1 select-none rounded-md text-[0.8rem] font-normal",r.weekday),week:(0,k.cn)("mt-2 flex w-full",r.week),week_number_header:(0,k.cn)("w-[--cell-size] select-none",r.week_number_header),week_number:(0,k.cn)("text-muted-foreground select-none text-[0.8rem]",r.week_number),day:(0,k.cn)("group/day relative aspect-square h-full w-full select-none p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md",r.day),range_start:(0,k.cn)("bg-accent rounded-l-md",r.range_start),range_middle:(0,k.cn)("rounded-none",r.range_middle),range_end:(0,k.cn)("bg-accent rounded-r-md",r.range_end),today:(0,k.cn)("bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none",r.today),outside:(0,k.cn)("text-muted-foreground aria-selected:text-muted-foreground",r.outside),disabled:(0,k.cn)("text-muted-foreground opacity-50",r.disabled),hidden:(0,k.cn)("invisible",r.hidden),...b},components:{Root:({className:a,rootRef:b,...c})=>(0,d.jsx)("div",{"data-slot":"calendar",ref:b,className:(0,k.cn)(a),...c}),Chevron:({className:a,orientation:b,...c})=>"left"===b?(0,d.jsx)(f.A,{className:(0,k.cn)("size-4",a),...c}):"right"===b?(0,d.jsx)(g.A,{className:(0,k.cn)("size-4",a),...c}):(0,d.jsx)(h.A,{className:(0,k.cn)("size-4",a),...c}),DayButton:n,WeekNumber:({children:a,...b})=>(0,d.jsx)("td",{...b,children:(0,d.jsx)("div",{className:"flex size-[--cell-size] items-center justify-center text-center",children:a})}),...p},...q})}function n({className:a,day:b,modifiers:c,...f}){let g=(0,i.a)(),h=e.useRef(null);return e.useEffect(()=>{c.focused&&h.current?.focus()},[c.focused]),(0,d.jsx)(l.$,{ref:h,variant:"ghost",size:"icon","data-day":b.date.toLocaleDateString(),"data-selected-single":c.selected&&!c.range_start&&!c.range_end&&!c.range_middle,"data-range-start":c.range_start,"data-range-end":c.range_end,"data-range-middle":c.range_middle,className:(0,k.cn)("data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 flex aspect-square h-auto w-full min-w-[--cell-size] flex-col gap-1 font-normal leading-none data-[range-end=true]:rounded-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] [&>span]:text-xs [&>span]:opacity-70",g.day,a),...f})}},59821:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(96241);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(a,b,c)=>{"use strict";c.d(b,{bq:()=>m,eb:()=>q,gC:()=>p,l6:()=>k,yv:()=>l});var d=c(60687),e=c(43210),f=c(72951),g=c(78272),h=c(3589),i=c(13964),j=c(96241);let k=f.bL;f.YJ;let l=f.WT,m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.l9,{ref:e,className:(0,j.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,d.jsx)(f.In,{asChild:!0,children:(0,d.jsx)(g.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=f.l9.displayName;let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.PP,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}));n.displayName=f.PP.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wn,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(g.A,{className:"h-4 w-4"})}));o.displayName=f.wn.displayName;let p=e.forwardRef(({className:a,children:b,position:c="popper",...e},g)=>(0,d.jsx)(f.ZL,{children:(0,d.jsxs)(f.UC,{ref:g,className:(0,j.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(n,{}),(0,d.jsx)(f.LM,{className:(0,j.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(o,{})]})}));p.displayName=f.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.JU,{ref:c,className:(0,j.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.q7,{ref:e,className:(0,j.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),(0,d.jsx)(f.p4,{children:b})]}));q.displayName=f.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=f.wv.displayName},65325:(a,b,c)=>{Promise.resolve().then(c.bind(c,38380))},66819:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}},67081:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(37413),e=c(54781),f=c(51358);function g(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(e.E,{className:"h-8 w-64"}),(0,d.jsx)(e.E,{className:"h-4 w-96 mt-2"})]}),(0,d.jsx)(e.E,{className:"h-10 w-40"})]}),(0,d.jsx)("div",{className:"grid gap-4 md:grid-cols-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(e.E,{className:"h-4 w-24"}),(0,d.jsx)(e.E,{className:"h-4 w-4"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)(e.E,{className:"h-8 w-16"}),(0,d.jsx)(e.E,{className:"h-3 w-20 mt-2"})]})]},b))}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"flex space-x-1",children:Array.from({length:4}).map((a,b)=>(0,d.jsx)(e.E,{className:"h-10 w-24"},b))}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(e.E,{className:"h-6 w-48"}),(0,d.jsx)(e.E,{className:"h-4 w-64"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(e.E,{className:"h-12 w-12 rounded-full"}),(0,d.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,d.jsx)(e.E,{className:"h-4 w-48"}),(0,d.jsx)(e.E,{className:"h-3 w-32"})]}),(0,d.jsx)(e.E,{className:"h-8 w-20"}),(0,d.jsx)(e.E,{className:"h-8 w-16"})]},b))})})]})]})]})}},75340:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>A});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(68988),i=c(39390),j=c(59821),k=c(85910),l=c(96752),m=c(37826),n=c(63974),o=c(56924),p=c(33135),q=c(62688);let r=(0,q.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var s=c(41312),t=c(48730),u=c(5336),v=c(92834),w=c(40228);let x=(0,q.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var y=c(85650),z=c(41585);function A(){let[a,b]=(0,e.useState)(),[c,q]=(0,e.useState)([{id:"EMP001",name:"Ana Garc\xeda",lastName:"L\xf3pez",document:"12345678",position:"Recepcionista",department:"Recepci\xf3n",phone:"+57 ************",email:"<EMAIL>",hireDate:"2023-01-15",salary:25e5,shift:"Ma\xf1ana",status:"activo",supervisor:"Mar\xeda Rodr\xedguez"},{id:"EMP002",name:"Carlos",lastName:"Ruiz",document:"87654321",position:"Camarero",department:"Limpieza",phone:"+57 ************",email:"<EMAIL>",hireDate:"2023-03-20",salary:22e5,shift:"Ma\xf1ana",status:"activo",supervisor:"Pedro G\xf3mez"},{id:"EMP003",name:"Mar\xeda",lastName:"L\xf3pez",document:"11223344",position:"Mesero",department:"Restaurante",phone:"+57 ************",email:"<EMAIL>",hireDate:"2023-02-10",salary:23e5,shift:"Tarde",status:"activo",supervisor:"Luis Mart\xedn"},{id:"EMP004",name:"Pedro",lastName:"G\xf3mez",document:"55667788",position:"Supervisor Limpieza",department:"Limpieza",phone:"+57 ************",email:"<EMAIL>",hireDate:"2022-11-05",salary:32e5,shift:"Ma\xf1ana",status:"vacaciones",supervisor:"Gerente General"}]),[A,B]=(0,e.useState)([{id:"T001",employeeId:"EMP001",employee:"Ana Garc\xeda",date:"2024-01-15",shift:"Ma\xf1ana",startTime:"06:00",endTime:"14:00",department:"Recepci\xf3n",status:"programado"},{id:"T002",employeeId:"EMP002",employee:"Carlos Ruiz",date:"2024-01-15",shift:"Ma\xf1ana",startTime:"06:00",endTime:"14:00",department:"Limpieza",status:"completado"},{id:"T003",employeeId:"EMP003",employee:"Mar\xeda L\xf3pez",date:"2024-01-15",shift:"Tarde",startTime:"14:00",endTime:"22:00",department:"Restaurante",status:"en_curso"}]),[C,D]=(0,e.useState)([{id:"AST001",employeeId:"EMP001",employee:"Ana Garc\xeda",date:"2024-01-15",entryTime:"06:00",exitTime:"14:00",regularHours:8,extraHours:0,status:"presente",notes:""},{id:"AST002",employeeId:"EMP002",employee:"Carlos Ruiz",date:"2024-01-15",entryTime:"06:15",exitTime:"14:30",regularHours:8,extraHours:.25,status:"tarde",notes:"Lleg\xf3 15 minutos tarde"},{id:"AST003",employeeId:"EMP003",employee:"Mar\xeda L\xf3pez",date:"2024-01-14",entryTime:"-",exitTime:"-",regularHours:0,extraHours:0,status:"ausente",notes:"Incapacidad m\xe9dica"}]),[E,F]=(0,e.useState)([{id:"PER001",employeeId:"EMP004",employee:"Pedro G\xf3mez",type:"Vacaciones",startDate:"2024-01-10",endDate:"2024-01-20",days:10,reason:"Vacaciones anuales",status:"aprobado",approvedBy:"Gerente General"},{id:"PER002",employeeId:"EMP001",employee:"Ana Garc\xeda",type:"Permiso Personal",startDate:"2024-01-18",endDate:"2024-01-18",days:1,reason:"Cita m\xe9dica",status:"pendiente",approvedBy:""}]),[G,H]=(0,e.useState)({name:"",lastName:"",document:"",position:"",department:"",phone:"",email:"",salary:"",shift:"",supervisor:""}),[I,J]=(0,e.useState)({employeeId:"",date:"",shift:"",startTime:"",endTime:"",department:""});return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Gesti\xf3n de Personal"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Administraci\xf3n de employees, shifts y asistencia"})]}),(0,d.jsxs)(m.lG,{children:[(0,d.jsx)(m.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{children:[(0,d.jsx)(r,{className:"mr-2 h-4 w-4"}),"Nuevo Empleado"]})}),(0,d.jsxs)(m.Cf,{className:"max-w-2xl",children:[(0,d.jsxs)(m.c7,{children:[(0,d.jsx)(m.L3,{children:"Nuevo Empleado"}),(0,d.jsx)(m.rr,{children:"Registrar un nuevo empleado en el sistema"})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"nombre",children:"Nombre"}),(0,d.jsx)(h.p,{id:"nombre",value:G.name,onChange:a=>H({...G,name:a.target.value}),placeholder:"Nombre"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"lastName",children:"Apellido"}),(0,d.jsx)(h.p,{id:"lastName",value:G.lastName,onChange:a=>H({...G,lastName:a.target.value}),placeholder:"Apellido"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"document",children:"Documento"}),(0,d.jsx)(h.p,{id:"document",value:G.document,onChange:a=>H({...G,document:a.target.value}),placeholder:"N\xfamero de documento"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"phone",children:"Tel\xe9fono"}),(0,d.jsx)(h.p,{id:"phone",value:G.phone,onChange:a=>H({...G,phone:a.target.value}),placeholder:"+57 ************"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"email",children:"Email"}),(0,d.jsx)(h.p,{id:"email",type:"email",value:G.email,onChange:a=>H({...G,email:a.target.value}),placeholder:"<EMAIL>"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"cargo",children:"Cargo"}),(0,d.jsxs)(n.l6,{value:G.position,onValueChange:a=>H({...G,position:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar cargo"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"Recepcionista",children:"Recepcionista"}),(0,d.jsx)(n.eb,{value:"Camarero",children:"Camarero"}),(0,d.jsx)(n.eb,{value:"Mesero",children:"Mesero"}),(0,d.jsx)(n.eb,{value:"Cocinero",children:"Cocinero"}),(0,d.jsx)(n.eb,{value:"Supervisor",children:"Supervisor"}),(0,d.jsx)(n.eb,{value:"Gerente",children:"Gerente"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"departamento",children:"Departamento"}),(0,d.jsxs)(n.l6,{value:G.department,onValueChange:a=>H({...G,department:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar departamento"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"Recepci\xf3n",children:"Recepci\xf3n"}),(0,d.jsx)(n.eb,{value:"Limpieza",children:"Limpieza"}),(0,d.jsx)(n.eb,{value:"Restaurante",children:"Restaurante"}),(0,d.jsx)(n.eb,{value:"Mantenimiento",children:"Mantenimiento"}),(0,d.jsx)(n.eb,{value:"Administraci\xf3n",children:"Administraci\xf3n"})]})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"salario",children:"Salario"}),(0,d.jsx)(h.p,{id:"salario",type:"number",value:G.salary,onChange:a=>H({...G,salary:a.target.value}),placeholder:"2500000"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"turno",children:"Turno"}),(0,d.jsxs)(n.l6,{value:G.shift,onValueChange:a=>H({...G,shift:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar turno"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"Ma\xf1ana",children:"Ma\xf1ana (6:00-14:00)"}),(0,d.jsx)(n.eb,{value:"Tarde",children:"Tarde (14:00-22:00)"}),(0,d.jsx)(n.eb,{value:"Noche",children:"Noche (22:00-6:00)"})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"supervisor",children:"Supervisor"}),(0,d.jsx)(h.p,{id:"supervisor",value:G.supervisor,onChange:a=>H({...G,supervisor:a.target.value}),placeholder:"Nombre del supervisor"})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsx)(g.$,{onClick:()=>{if(G.name&&G.lastName&&G.document){let a={id:`EMP${String(c.length+1).padStart(3,"0")}`,name:G.name,lastName:G.lastName,document:G.document,position:G.position,department:G.department,phone:G.phone,email:G.email,hireDate:new Date().toISOString().split("T")[0],salary:Number.parseInt(G.salary)||0,shift:G.shift,status:"activo",supervisor:G.supervisor};q([...c,a]),H({name:"",lastName:"",document:"",position:"",department:"",phone:"",email:"",salary:"",shift:"",supervisor:""})}},children:"Crear Empleado"})]})]})]})]})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Total Empleados"}),(0,d.jsx)(s.A,{className:"h-4 w-4 text-blue-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:c.length}),(0,d.jsxs)("p",{className:"text-xs text-muted-foreground",children:[c.filter(a=>"activo"===a.status).length," activos"]})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Turnos Hoy"}),(0,d.jsx)(t.A,{className:"h-4 w-4 text-green-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:A.filter(a=>a.date===new Date().toISOString().split("T")[0]).length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Programados"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Asistencia Hoy"}),(0,d.jsx)(u.A,{className:"h-4 w-4 text-purple-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsxs)("div",{className:"text-2xl font-bold",children:[Math.round(C.filter(a=>a.date===new Date().toISOString().split("T")[0]&&"presente"===a.status).length/C.filter(a=>a.date===new Date().toISOString().split("T")[0]).length*100)||0,"%"]}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Empleados presentes"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Permisos Pendientes"}),(0,d.jsx)(v.A,{className:"h-4 w-4 text-orange-600"})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:E.filter(a=>"pendiente"===a.status).length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Por aprobar"})]})]})]}),(0,d.jsxs)(k.tU,{defaultValue:"employees",className:"space-y-4",children:[(0,d.jsxs)(k.j7,{children:[(0,d.jsx)(k.Xi,{value:"employees",children:"Empleados"}),(0,d.jsx)(k.Xi,{value:"shifts",children:"Turnos"}),(0,d.jsx)(k.Xi,{value:"asistencia",children:"Asistencia"}),(0,d.jsx)(k.Xi,{value:"permisos",children:"Permisos"})]}),(0,d.jsx)(k.av,{value:"employees",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Lista de Empleados"}),(0,d.jsx)(f.BT,{children:"Gesti\xf3n de informaci\xf3n de employees"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Empleado"}),(0,d.jsx)(l.nd,{children:"Documento"}),(0,d.jsx)(l.nd,{children:"Cargo"}),(0,d.jsx)(l.nd,{children:"Departamento"}),(0,d.jsx)(l.nd,{children:"Turno"}),(0,d.jsx)(l.nd,{children:"Salario"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:c.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"font-medium",children:[a.name," ",a.lastName]}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.email})]})}),(0,d.jsx)(l.nA,{children:a.document}),(0,d.jsx)(l.nA,{children:a.position}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(j.E,{variant:"outline",children:a.department})}),(0,d.jsx)(l.nA,{children:a.shift}),(0,d.jsxs)(l.nA,{children:["$",a.salary.toLocaleString()]}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"activo":return(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Activo"});case"vacaciones":return(0,d.jsx)(j.E,{className:"bg-blue-100 text-blue-800",children:"Vacaciones"});case"incapacidad":return(0,d.jsx)(j.E,{className:"bg-yellow-100 text-yellow-800",children:"Incapacidad"});case"inactivo":return(0,d.jsx)(j.E,{variant:"secondary",children:"Inactivo"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(a.status)}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(g.$,{variant:"outline",size:"sm",children:"Editar"})})]},a.id))})]})})]})}),(0,d.jsxs)(k.av,{value:"shifts",className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("div",{}),(0,d.jsxs)(m.lG,{children:[(0,d.jsx)(m.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{children:[(0,d.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Programar Turno"]})}),(0,d.jsxs)(m.Cf,{children:[(0,d.jsxs)(m.c7,{children:[(0,d.jsx)(m.L3,{children:"Programar Turno"}),(0,d.jsx)(m.rr,{children:"Asignar turno a un empleado"})]}),(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"employee",children:"Empleado"}),(0,d.jsxs)(n.l6,{value:I.employeeId,onValueChange:a=>J({...I,employeeId:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar empleado"})}),(0,d.jsx)(n.gC,{children:c.map(a=>(0,d.jsxs)(n.eb,{value:a.id,children:[a.name," ",a.lastName," - ",a.position]},a.id))})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{children:"Fecha"}),(0,d.jsxs)(p.AM,{children:[(0,d.jsx)(p.Wv,{asChild:!0,children:(0,d.jsxs)(g.$,{variant:"outline",className:"w-full justify-start text-left font-normal bg-transparent",children:[(0,d.jsx)(w.A,{className:"mr-2 h-4 w-4"}),a?(0,y.GP)(a,"PPP",{locale:z.es}):"Seleccionar fecha"]})}),(0,d.jsx)(p.hl,{className:"w-auto p-0",children:(0,d.jsx)(o.V,{mode:"single",selected:a,onSelect:a=>{b(a),J({...I,date:a?a.toISOString().split("T")[0]:""})},initialFocus:!0})})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"turno",children:"Turno"}),(0,d.jsxs)(n.l6,{value:I.shift,onValueChange:a=>J({...I,shift:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar turno"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"Ma\xf1ana",children:"Ma\xf1ana"}),(0,d.jsx)(n.eb,{value:"Tarde",children:"Tarde"}),(0,d.jsx)(n.eb,{value:"Noche",children:"Noche"})]})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"horaInicio",children:"Hora Inicio"}),(0,d.jsx)(h.p,{id:"horaInicio",type:"time",value:I.startTime,onChange:a=>J({...I,startTime:a.target.value})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"horaFin",children:"Hora Fin"}),(0,d.jsx)(h.p,{id:"horaFin",type:"time",value:I.endTime,onChange:a=>J({...I,endTime:a.target.value})})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"departamento",children:"Departamento"}),(0,d.jsxs)(n.l6,{value:I.department,onValueChange:a=>J({...I,department:a}),children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Seleccionar departamento"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"Recepci\xf3n",children:"Recepci\xf3n"}),(0,d.jsx)(n.eb,{value:"Limpieza",children:"Limpieza"}),(0,d.jsx)(n.eb,{value:"Restaurante",children:"Restaurante"}),(0,d.jsx)(n.eb,{value:"Mantenimiento",children:"Mantenimiento"}),(0,d.jsx)(n.eb,{value:"Administraci\xf3n",children:"Administraci\xf3n"})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",children:"Cancelar"}),(0,d.jsx)(g.$,{onClick:()=>{if(I.employeeId&&I.date&&I.shift){let a=c.find(a=>a.id===I.employeeId),b={id:`T${String(A.length+1).padStart(3,"0")}`,employeeId:I.employeeId,employee:a?`${a.name} ${a.lastName}`:"",date:I.date,shift:I.shift,startTime:I.startTime,endTime:I.endTime,department:I.department,status:"programado"};B([...A,b]),J({employeeId:"",date:"",shift:"",startTime:"",endTime:"",department:""})}},children:"Programar Turno"})]})]})]})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Programaci\xf3n de Turnos"}),(0,d.jsx)(f.BT,{children:"Gesti\xf3n de horarios y shifts de trabajo"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Empleado"}),(0,d.jsx)(l.nd,{children:"Fecha"}),(0,d.jsx)(l.nd,{children:"Turno"}),(0,d.jsx)(l.nd,{children:"Horario"}),(0,d.jsx)(l.nd,{children:"Departamento"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:A.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{className:"font-medium",children:a.employee}),(0,d.jsx)(l.nA,{children:a.date}),(0,d.jsx)(l.nA,{children:a.shift}),(0,d.jsxs)(l.nA,{children:[a.startTime," - ",a.endTime]}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(j.E,{variant:"outline",children:a.department})}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"programado":return(0,d.jsx)(j.E,{className:"bg-blue-100 text-blue-800",children:"Programado"});case"en_curso":return(0,d.jsx)(j.E,{className:"bg-yellow-100 text-yellow-800",children:"En Curso"});case"completado":return(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Completado"});case"ausente":return(0,d.jsx)(j.E,{variant:"destructive",children:"Ausente"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(a.status)}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(g.$,{variant:"outline",size:"sm",children:"Editar"})})]},a.id))})]})})]})]}),(0,d.jsx)(k.av,{value:"asistencia",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Control de Asistencia"}),(0,d.jsx)(f.BT,{children:"Registro de entrada y salida de employees"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Empleado"}),(0,d.jsx)(l.nd,{children:"Fecha"}),(0,d.jsx)(l.nd,{children:"Entrada"}),(0,d.jsx)(l.nd,{children:"Salida"}),(0,d.jsx)(l.nd,{children:"Horas Reg."}),(0,d.jsx)(l.nd,{children:"Horas Extra"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Observaciones"})]})}),(0,d.jsx)(l.BF,{children:C.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{className:"font-medium",children:a.employee}),(0,d.jsx)(l.nA,{children:a.date}),(0,d.jsx)(l.nA,{children:a.entryTime}),(0,d.jsx)(l.nA,{children:a.exitTime}),(0,d.jsxs)(l.nA,{children:[a.regularHours,"h"]}),(0,d.jsxs)(l.nA,{children:[a.extraHours,"h"]}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"presente":return(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Presente"});case"tarde":return(0,d.jsx)(j.E,{className:"bg-yellow-100 text-yellow-800",children:"Tarde"});case"ausente":return(0,d.jsx)(j.E,{variant:"destructive",children:"Ausente"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(a.status)}),(0,d.jsx)(l.nA,{children:a.notes})]},a.id))})]})})]})}),(0,d.jsx)(k.av,{value:"permisos",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Solicitudes de Permisos"}),(0,d.jsx)(f.BT,{children:"Gesti\xf3n de vacaciones y permisos"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{children:"Empleado"}),(0,d.jsx)(l.nd,{children:"Tipo"}),(0,d.jsx)(l.nd,{children:"Fechas"}),(0,d.jsx)(l.nd,{children:"D\xedas"}),(0,d.jsx)(l.nd,{children:"Motivo"}),(0,d.jsx)(l.nd,{children:"Estado"}),(0,d.jsx)(l.nd,{children:"Acciones"})]})}),(0,d.jsx)(l.BF,{children:E.map(a=>(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{className:"font-medium",children:a.employee}),(0,d.jsx)(l.nA,{children:(0,d.jsx)(j.E,{variant:"outline",children:a.type})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsx)("div",{children:a.startDate}),(0,d.jsx)("div",{children:a.endDate})]})}),(0,d.jsx)(l.nA,{children:a.days}),(0,d.jsx)(l.nA,{children:a.reason}),(0,d.jsx)(l.nA,{children:(a=>{switch(a){case"aprobado":return(0,d.jsx)(j.E,{className:"bg-green-100 text-green-800",children:"Aprobado"});case"pendiente":return(0,d.jsx)(j.E,{className:"bg-yellow-100 text-yellow-800",children:"Pendiente"});case"rechazado":return(0,d.jsx)(j.E,{variant:"destructive",children:"Rechazado"});default:return(0,d.jsx)(j.E,{variant:"secondary",children:a})}})(a.status)}),(0,d.jsx)(l.nA,{children:(0,d.jsx)("div",{className:"flex space-x-2",children:"pendiente"===a.status&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(g.$,{size:"sm",onClick:()=>{var b;return b=a.id,void F(E.map(a=>a.id===b?{...a,status:"aprobado",approvedBy:"Usuario Actual"}:a))},children:[(0,d.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Aprobar"]}),(0,d.jsxs)(g.$,{size:"sm",variant:"outline",onClick:()=>{var b;return b=a.id,void F(E.map(a=>a.id===b?{...a,status:"rechazado",approvedBy:"Usuario Actual"}:a))},children:[(0,d.jsx)(x,{className:"mr-2 h-4 w-4"}),"Rechazar"]})]})})})]},a.id))})]})})]})})]})]})}},78335:()=>{},85910:(a,b,c)=>{"use strict";c.d(b,{Xi:()=>j,av:()=>k,j7:()=>i,tU:()=>h});var d=c(60687),e=c(43210),f=c(55146),g=c(96241);let h=f.bL,i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.B8,{ref:c,className:(0,g.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...b}));i.displayName=f.B8.displayName;let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.l9,{ref:c,className:(0,g.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...b}));j.displayName=f.l9.displayName;let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.UC,{ref:c,className:(0,g.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...b}));k.displayName=f.UC.displayName},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{},96752:(a,b,c)=>{"use strict";c.d(b,{A0:()=>h,BF:()=>i,Hj:()=>j,XI:()=>g,nA:()=>l,nd:()=>k});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{className:"relative w-full overflow-auto",children:(0,d.jsx)("table",{ref:c,className:(0,f.cn)("w-full caption-bottom text-sm",a),...b})}));g.displayName="Table";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("thead",{ref:c,className:(0,f.cn)("[&_tr]:border-b",a),...b}));h.displayName="TableHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tbody",{ref:c,className:(0,f.cn)("[&_tr:last-child]:border-0",a),...b}));i.displayName="TableBody",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tfoot",{ref:c,className:(0,f.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...b})).displayName="TableFooter";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tr",{ref:c,className:(0,f.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...b}));j.displayName="TableRow";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("th",{ref:c,className:(0,f.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...b}));k.displayName="TableHead";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("td",{ref:c,className:(0,f.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...b}));l.displayName="TableCell",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("caption",{ref:c,className:(0,f.cn)("mt-4 text-sm text-muted-foreground",a),...b})).displayName="TableCaption"},97318:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["personal",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,38380)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\personal\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(c.bind(c,67081)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\personal\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\personal\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/personal/page",pathname:"/personal",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/personal/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[438,271,17,385,957],()=>b(b.s=97318));module.exports=c})();