{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/acerca-de", "regex": "^/acerca\\-de(?:/)?$", "routeKeys": {}, "namedRegex": "^/acerca\\-de(?:/)?$"}, {"page": "/configuracion", "regex": "^/configuracion(?:/)?$", "routeKeys": {}, "namedRegex": "^/configuracion(?:/)?$"}, {"page": "/contabilidad", "regex": "^/contabilidad(?:/)?$", "routeKeys": {}, "namedRegex": "^/contabilidad(?:/)?$"}, {"page": "/eventos", "regex": "^/eventos(?:/)?$", "routeKeys": {}, "namedRegex": "^/eventos(?:/)?$"}, {"page": "/facturacion", "regex": "^/facturacion(?:/)?$", "routeKeys": {}, "namedRegex": "^/facturacion(?:/)?$"}, {"page": "/inventario", "regex": "^/inventario(?:/)?$", "routeKeys": {}, "namedRegex": "^/inventario(?:/)?$"}, {"page": "/limpieza", "regex": "^/limpieza(?:/)?$", "routeKeys": {}, "namedRegex": "^/limpieza(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/marketing", "regex": "^/marketing(?:/)?$", "routeKeys": {}, "namedRegex": "^/marketing(?:/)?$"}, {"page": "/parqueadero", "regex": "^/parqueadero(?:/)?$", "routeKeys": {}, "namedRegex": "^/parqueadero(?:/)?$"}, {"page": "/perfil", "regex": "^/perfil(?:/)?$", "routeKeys": {}, "namedRegex": "^/perfil(?:/)?$"}, {"page": "/personal", "regex": "^/personal(?:/)?$", "routeKeys": {}, "namedRegex": "^/personal(?:/)?$"}, {"page": "/recepcion", "regex": "^/recepcion(?:/)?$", "routeKeys": {}, "namedRegex": "^/recepcion(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/reportes", "regex": "^/reportes(?:/)?$", "routeKeys": {}, "namedRegex": "^/reportes(?:/)?$"}, {"page": "/reservas", "regex": "^/reservas(?:/)?$", "routeKeys": {}, "namedRegex": "^/reservas(?:/)?$"}, {"page": "/restaurante", "regex": "^/restaurante(?:/)?$", "routeKeys": {}, "namedRegex": "^/restaurante(?:/)?$"}, {"page": "/test-responsive", "regex": "^/test\\-responsive(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-responsive(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}}