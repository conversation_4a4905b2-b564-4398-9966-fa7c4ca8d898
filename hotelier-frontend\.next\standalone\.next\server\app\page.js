(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1851:(a,b,c)=>{Promise.resolve().then(c.bind(c,26527))},2041:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(92491),e=c(90812),f=c(27747),g=c(9920),h=c(84629),i=(0,d.gu)({chartName:"BarChart",GraphicalChild:e.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:f.W},{axisType:"yAxis",AxisComp:g.h}],formatAxisMap:h.pr})},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19778:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,90597)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}],H=["C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},26527:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y});var d=c(60687),e=c(55192),f=c(59821),g=c(41862),h=c(5263),i=c(23928),j=c(93508),k=c(92834),l=c(41312),m=c(94577),n=c(10022),o=c(30920),p=c(53892),q=c(69206),r=c(25679),s=c(2041),t=c(27747),u=c(9920),v=c(90812),w=c(43210);c(59556);var x=c(71702);function y(){let[a,b]=(0,w.useState)(!1),[c,y]=(0,w.useState)(!0),[z,A]=(0,w.useState)(null),[B,C]=(0,w.useState)([]),{toast:D}=(0,x.dj)();if(c)return(0,d.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,d.jsx)(g.A,{className:"h-8 w-8 animate-spin"}),(0,d.jsx)("span",{className:"ml-2",children:"Cargando dashboard..."})]});let E=[{title:"Ocupaci\xf3n Actual",value:`${z?.stats?.occupancyRate||0}%`,description:`${z?.stats?.occupiedRooms||0} de ${z?.stats?.totalRooms||0} habitaciones`,icon:h.A,color:"text-blue-600"},{title:"Ingresos del Mes",value:`$${(z?.stats?.monthlyRevenue||0).toLocaleString()}`,description:"Mes actual",icon:i.A,color:"text-green-600"},{title:"Reservas Activas",value:z?.stats?.activeReservations?.toString()||"0",description:"Confirmadas y check-in",icon:j.A,color:"text-orange-600"},{title:"Total Habitaciones",value:z?.stats?.totalRooms?.toString()||"0",description:"Disponibles en el hotel",icon:h.A,color:"text-purple-600"}],F=[{name:"Ocupadas",value:z?.stats?.occupiedRooms||0,fill:"hsl(var(--chart-1))"},{name:"Disponibles",value:(z?.stats?.totalRooms||0)-(z?.stats?.occupiedRooms||0),fill:"hsl(var(--chart-2))"}],G=z?.recentReservations?.slice(0,5).map((a,b)=>({time:new Date(a.createdAt).toLocaleTimeString("es",{hour:"2-digit",minute:"2-digit"}),activity:`Nueva reserva - ${a.guestName} - Habitaci\xf3n ${a.room?.number}`,type:"reservation"}))||[{time:"10:30",activity:"No hay actividad reciente",type:"info"}],H=[...z?.upcomingArrivals?.slice(0,2).map(a=>({date:`Hoy ${a.checkInDate?new Date(a.checkInDate).toLocaleTimeString("es",{hour:"2-digit",minute:"2-digit"}):""}`,event:`Check-in - ${a.guestName} - Habitaci\xf3n ${a.room?.number}`,attendees:a.guests}))||[],...z?.upcomingDepartures?.slice(0,2).map(a=>({date:`Hoy ${a.checkOutDate?new Date(a.checkOutDate).toLocaleTimeString("es",{hour:"2-digit",minute:"2-digit"}):""}`,event:`Check-out - ${a.guestName} - Habitaci\xf3n ${a.room?.number}`,attendees:a.guests}))||[]];0===H.length&&H.push({date:"Hoy",event:"No hay eventos programados",attendees:0});let I={revenue:{label:"Ingresos"},occupied:{label:"Ocupadas"},available:{label:"Disponibles"}};return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Panel de Control Hotelier"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Resumen general del hotel"})]}),(0,d.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:E.map((a,b)=>(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(e.ZB,{className:"text-sm font-medium",children:a.title}),(0,d.jsx)(a.icon,{className:`h-4 w-4 ${a.color}`})]}),(0,d.jsxs)(e.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:a.value}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:a.description})]})]},b))}),(0,d.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,d.jsxs)(e.Zp,{className:"lg:col-span-2",children:[(0,d.jsxs)(e.aR,{children:[(0,d.jsx)(e.ZB,{children:"Actividad Reciente"}),(0,d.jsx)(e.BT,{children:"\xdaltimas actividades del hotel"})]}),(0,d.jsx)(e.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:G.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"text-sm text-muted-foreground min-w-[50px]",children:a.time}),(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsx)("p",{className:"text-sm",children:a.activity})}),(0,d.jsx)(f.E,{variant:"checkin"===a.type?"default":"cleaning"===a.type?"secondary":"service"===a.type?"outline":"reservation"===a.type?"default":"destructive",children:"checkin"===a.type?"Check-in":"cleaning"===a.type?"Limpieza":"service"===a.type?"Servicio":"reservation"===a.type?"Reserva":"Check-out"})]},b))})})]}),(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{children:[(0,d.jsx)(e.ZB,{children:"Pr\xf3ximos Eventos"}),(0,d.jsx)(e.BT,{children:"Eventos programados"})]}),(0,d.jsx)(e.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:H.map((a,b)=>(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"text-sm font-medium",children:a.event}),(0,d.jsx)("div",{className:"text-xs text-muted-foreground",children:a.date}),(0,d.jsx)("div",{className:"text-xs",children:(0,d.jsxs)(f.E,{variant:"outline",children:[a.attendees," asistentes"]})})]},b))})})]})]}),(0,d.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{children:[(0,d.jsx)(e.ZB,{children:"Ocupaci\xf3n de Habitaciones"}),(0,d.jsx)(e.BT,{children:"Distribuci\xf3n actual de habitaciones"})]}),(0,d.jsx)(e.Wu,{children:a?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(o.at,{config:I,className:"mx-auto aspect-square max-h-[300px]",children:(0,d.jsxs)(p.r,{children:[(0,d.jsx)(o.II,{cursor:!1,content:(0,d.jsx)(o.Nt,{hideLabel:!0})}),(0,d.jsx)(q.F,{data:F,dataKey:"value",nameKey:"name",innerRadius:60,strokeWidth:5,children:F.map((a,b)=>(0,d.jsx)(r.f,{fill:a.fill},`cell-${b}`))})]})}),(0,d.jsxs)("div",{className:"flex justify-center space-x-4 mt-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-chart-1 rounded-full"}),(0,d.jsxs)("span",{className:"text-sm",children:["Ocupadas (",z?.stats?.occupiedRooms||0,")"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-chart-2 rounded-full"}),(0,d.jsxs)("span",{className:"text-sm",children:["Disponibles (",(z?.stats?.totalRooms||0)-(z?.stats?.occupiedRooms||0),")"]})]})]})]}):(0,d.jsx)("div",{className:"mx-auto aspect-square max-h-[300px] flex items-center justify-center",children:(0,d.jsx)("div",{className:"text-muted-foreground",children:"Cargando gr\xe1fica..."})})})]}),(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{children:[(0,d.jsx)(e.ZB,{children:"Ingresos Mensuales"}),(0,d.jsx)(e.BT,{children:"Ingresos de los \xfaltimos 7 meses"})]}),(0,d.jsx)(e.Wu,{children:a?(0,d.jsx)(o.at,{config:I,className:"h-[300px]",children:(0,d.jsxs)(s.E,{accessibilityLayer:!0,data:B,children:[(0,d.jsx)(t.W,{dataKey:"month",tickLine:!1,tickMargin:10,axisLine:!1}),(0,d.jsx)(u.h,{tickLine:!1,axisLine:!1,tickFormatter:a=>`$${a.toLocaleString()}`}),(0,d.jsx)(o.II,{cursor:!1,content:(0,d.jsx)(o.Nt,{})}),(0,d.jsx)(v.y,{dataKey:"revenue",fill:"hsl(var(--chart-1))",radius:[4,4,0,0]})]})}):(0,d.jsx)("div",{className:"h-[300px] flex items-center justify-center",children:(0,d.jsx)("div",{className:"text-muted-foreground",children:"Cargando gr\xe1fica..."})})})]})]}),(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{children:[(0,d.jsx)(e.ZB,{children:"Acciones R\xe1pidas"}),(0,d.jsx)(e.BT,{children:"Accesos directos a funciones principales"})]}),(0,d.jsx)(e.Wu,{children:(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-lg hover:bg-accent cursor-pointer",children:[(0,d.jsx)(k.A,{className:"h-5 w-5 text-blue-600"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Nueva Reserva"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-lg hover:bg-accent cursor-pointer",children:[(0,d.jsx)(l.A,{className:"h-5 w-5 text-green-600"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Check-in R\xe1pido"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-lg hover:bg-accent cursor-pointer",children:[(0,d.jsx)(m.A,{className:"h-5 w-5 text-orange-600"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Pedido Room Service"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-lg hover:bg-accent cursor-pointer",children:[(0,d.jsx)(n.A,{className:"h-5 w-5 text-purple-600"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Generar Reporte"})]})]})})]})]})})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30920:(a,b,c)=>{"use strict";c.d(b,{II:()=>o,Nt:()=>p,at:()=>m});var d=c(60687),e=c(43210),f=c(48482),g=c(38246),h=c(57359),i=c(96241);let j={light:"",dark:".dark"},k=e.createContext(null);function l(){let a=e.useContext(k);if(!a)throw Error("useChart must be used within a <ChartContainer />");return a}let m=e.forwardRef(({id:a,className:b,children:c,config:g,...h},j)=>{let l=e.useId(),m=`chart-${a||l.replace(/:/g,"")}`;return(0,d.jsx)(k.Provider,{value:{config:g},children:(0,d.jsxs)("div",{"data-chart":m,ref:j,className:(0,i.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",b),...h,children:[(0,d.jsx)(n,{id:m,config:g}),(0,d.jsx)(f.u,{children:c})]})})});m.displayName="Chart";let n=({id:a,config:b})=>{let c=Object.entries(b).filter(([,a])=>a.theme||a.color);return c.length?(0,d.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(j).map(([b,d])=>`
${d} [data-chart=${a}] {
${c.map(([a,c])=>{let d=c.theme?.[b]||c.color;return d?`  --color-${a}: ${d};`:null}).join("\n")}
}
`).join("\n")}}):null},o=g.m,p=e.forwardRef(({active:a,payload:b,className:c,indicator:f="dot",hideLabel:g=!1,hideIndicator:h=!1,label:j,labelFormatter:k,labelClassName:m,formatter:n,color:o,nameKey:p,labelKey:r},s)=>{let{config:t}=l(),u=e.useMemo(()=>{if(g||!b?.length)return null;let[a]=b,c=`${r||a?.dataKey||a?.name||"value"}`,e=q(t,a,c),f=r||"string"!=typeof j?e?.label:t[j]?.label||j;return k?(0,d.jsx)("div",{className:(0,i.cn)("font-medium",m),children:k(f,b)}):f?(0,d.jsx)("div",{className:(0,i.cn)("font-medium",m),children:f}):null},[j,k,b,g,m,t,r]);if(!a||!b?.length)return null;let v=1===b.length&&"dot"!==f;return(0,d.jsxs)("div",{ref:s,className:(0,i.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",c),children:[v?null:u,(0,d.jsx)("div",{className:"grid gap-1.5",children:b.map((a,b)=>{let c=`${p||a.name||a.dataKey||"value"}`,e=q(t,a,c),g=o||a.payload.fill||a.color;return(0,d.jsx)("div",{className:(0,i.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===f&&"items-center"),children:n&&a?.value!==void 0&&a.name?n(a.value,a.name,a,b,a.payload):(0,d.jsxs)(d.Fragment,{children:[e?.icon?(0,d.jsx)(e.icon,{}):!h&&(0,d.jsx)("div",{className:(0,i.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===f,"w-1":"line"===f,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===f,"my-0.5":v&&"dashed"===f}),style:{"--color-bg":g,"--color-border":g}}),(0,d.jsxs)("div",{className:(0,i.cn)("flex flex-1 justify-between leading-none",v?"items-end":"items-center"),children:[(0,d.jsxs)("div",{className:"grid gap-1.5",children:[v?u:null,(0,d.jsx)("span",{className:"text-muted-foreground",children:e?.label||a.name})]}),a.value&&(0,d.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:a.value.toLocaleString()})]})]})},a.dataKey)})})]})});function q(a,b,c){if("object"!=typeof b||null===b)return;let d="payload"in b&&"object"==typeof b.payload&&null!==b.payload?b.payload:void 0,e=c;return c in b&&"string"==typeof b[c]?e=b[c]:d&&c in d&&"string"==typeof d[c]&&(e=d[c]),e in a?a[e]:a[c]}p.displayName="ChartTooltip",h.s,e.forwardRef(({className:a,hideIcon:b=!1,payload:c,verticalAlign:e="bottom",nameKey:f},g)=>{let{config:h}=l();return c?.length?(0,d.jsx)("div",{ref:g,className:(0,i.cn)("flex items-center justify-center gap-4","top"===e?"pb-3":"pt-3",a),children:c.map(a=>{let c=`${f||a.dataKey||"value"}`,e=q(h,a,c);return(0,d.jsxs)("div",{className:(0,i.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[e?.icon&&!b?(0,d.jsx)(e.icon,{}):(0,d.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:a.color}}),e?.label]},a.value)})}):null}).displayName="ChartLegend"},33873:a=>{"use strict";a.exports=require("path")},36195:(a,b,c)=>{Promise.resolve().then(c.bind(c,90597))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},59821:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(96241);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90597:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\Hotelier\\\\hotelier-frontend\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Github\\Hotelier\\hotelier-frontend\\app\\page.tsx","default")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[438,436,957],()=>b(b.s=19778));module.exports=c})();